import { getSubscribedUser } from '@/lib/auth';
import { parseError } from '@/lib/error/parse';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Create a rate limiter for the chat API
const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-chat',
});

export const POST = async (req: Request) => {
  console.log('🔥 Chat API: Request received');

  try {
    console.log('🔐 Chat API: Checking user authentication...');
    const user = await getSubscribedUser();
    console.log('✅ Chat API: User authenticated:', { userId: user?.id });
  } catch (error) {
    console.error('❌ Chat API: Authentication failed:', error);
    const message = parseError(error);
    return new Response(message, { status: 401 });
  }

  // Apply rate limiting
  if (process.env.NODE_ENV === 'production') {
    console.log('🚦 Chat API: Applying rate limiting...');
    const ip = req.headers.get('x-forwarded-for') || 'anonymous';
    const { success, limit, reset, remaining } = await rateLimiter.limit(ip);

    if (!success) {
      console.warn('⚠️ Chat API: Rate limit exceeded for IP:', ip);
      return new Response('Too many requests', {
        status: 429,
        headers: {
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': reset.toString(),
        },
      });
    }
    console.log('✅ Chat API: Rate limit check passed');
  } else {
    console.log('🔧 Chat API: Development mode - skipping rate limiting');
  }

  let requestBody;
  try {
    requestBody = await req.json();
    console.log('📥 Chat API: Request body parsed:', requestBody);
  } catch (error) {
    console.error('❌ Chat API: Failed to parse request body:', error);
    return new Response('Invalid JSON in request body', { status: 400 });
  }

  const { messages, modelId } = requestBody;

  console.log('🔍 Chat API: Validating request parameters...', {
    modelId,
    messagesCount: messages?.length,
    hasMessages: !!messages,
  });

  if (typeof modelId !== 'string') {
    console.error('❌ Chat API: Invalid modelId type:', typeof modelId);
    return new Response('Model must be a string', { status: 400 });
  }

  if (!Array.isArray(messages) || messages.length === 0) {
    console.error('❌ Chat API: Invalid messages:', messages);
    return new Response('Messages must be a non-empty array', { status: 400 });
  }

  console.log('🔍 Chat API: Looking for model:', modelId);
  console.log(
    '📋 Chat API: Available models:',
    textModels.flatMap((group) =>
      group.models.map((m) => ({ id: m.id, label: m.label }))
    )
  );

  const model = textModels
    .flatMap((m) => m.models)
    .find((m) => m.id === modelId);

  if (!model) {
    console.error('❌ Chat API: Model not found:', modelId);
    return new Response(`Invalid model: ${modelId}`, { status: 400 });
  }

  console.log('✅ Chat API: Model found:', {
    id: model.id,
    label: model.label,
  });

  // Check if API key is available for the model
  const modelProvider = model.model.modelId;
  console.log('🔑 Chat API: Model provider:', modelProvider);

  // Log environment variables (without exposing the actual keys)
  const envKeys = {
    OPENAI_API_KEY: !!process.env.OPENAI_API_KEY,
    ANTHROPIC_API_KEY: !!process.env.ANTHROPIC_API_KEY,
    GOOGLE_GENERATIVE_AI_API_KEY: !!process.env.GOOGLE_GENERATIVE_AI_API_KEY,
    GROQ_API_KEY: !!process.env.GROQ_API_KEY,
    DEEPSEEK_API_KEY: !!process.env.DEEPSEEK_API_KEY,
    XAI_API_KEY: !!process.env.XAI_API_KEY,
  };
  console.log('🔑 Chat API: Environment keys status:', envKeys);

  try {
    console.log('🚀 Chat API: Starting text generation...');

    // Use the system message from the request instead of overriding it
    const systemMessage =
      messages.find((m) => m.role === 'system')?.content ||
      "You are a helpful assistant that synthesizes content based on the user's prompts.";

    console.log('📝 Chat API: System message:', systemMessage);
    console.log(
      '💬 Chat API: User messages:',
      messages.filter((m) => m.role === 'user')
    );

    const result = streamText({
      model: model.model,
      system: systemMessage,
      messages: messages.filter((m) => m.role !== 'system'), // Remove system message from messages array
      onFinish: async ({ usage }) => {
        console.log('📊 Chat API: Generation finished. Usage:', usage);
        try {
          await trackCreditUsage({
            action: 'chat',
            cost: model.getCost({
              input: usage.promptTokens,
              output: usage.completionTokens,
            }),
          });
          console.log('✅ Chat API: Credit usage tracked');
        } catch (creditError) {
          console.error(
            '⚠️ Chat API: Failed to track credit usage:',
            creditError
          );
        }
      },
    });

    console.log('✅ Chat API: Returning streaming response');
    return result.toDataStreamResponse();
  } catch (error) {
    console.error('❌ Chat API: Text generation failed:', error);
    console.error('❌ Chat API: Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
    });

    const errorMessage =
      error instanceof Error ? error.message : 'An error occurred';
    return new Response(`Error: ${errorMessage}`, { status: 500 });
  }
};
