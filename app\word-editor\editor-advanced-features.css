/*
   Advanced Editor Features for Tersa Word Editor
   Focus mode, better text contrast, and enhanced writing experience
*/

/* ===== FOCUS MODE ===== */

.editor-focus-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  background: hsl(var(--background)) !important;
  padding: 2rem !important;
  overflow-y: auto !important;
}

.editor-focus-mode .editor-card {
  max-width: 800px !important;
  margin: 0 auto !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.editor-focus-mode .editor-header {
  background: transparent !important;
  border: none !important;
  padding: 1rem 0 !important;
}

.editor-focus-mode .editor-footer {
  background: transparent !important;
  border: none !important;
  position: fixed !important;
  bottom: 1rem !important;
  right: 2rem !important;
  left: auto !important;
  width: auto !important;
  padding: 0.75rem 1rem !important;
  border-radius: 12px !important;
  background: hsl(var(--background) / 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

.editor-focus-mode .ProseMirror {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  padding: 3rem 2rem !important;
  font-size: 1.25rem !important;
  line-height: 1.8 !important;
  max-width: none !important;
}

/* Focus mode toggle button */
.focus-mode-toggle {
  position: fixed !important;
  top: 2rem !important;
  right: 2rem !important;
  z-index: 10000 !important;
  background: hsl(var(--background) / 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 12px !important;
  padding: 0.75rem !important;
  transition: all 0.3s ease !important;
}

.focus-mode-toggle:hover {
  background: hsl(var(--accent) / 0.1) !important;
  border-color: hsl(var(--accent) / 0.4) !important;
  transform: scale(1.05) !important;
}

/* ===== ENHANCED TEXT CONTRAST ===== */

.high-contrast-mode .ProseMirror {
  color: hsl(var(--foreground)) !important;
  background: hsl(var(--background)) !important;
}

.high-contrast-mode .ProseMirror h1,
.high-contrast-mode .ProseMirror h2,
.high-contrast-mode .ProseMirror h3 {
  color: hsl(var(--foreground)) !important;
  font-weight: 800 !important;
}

.high-contrast-mode .ProseMirror p {
  color: hsl(var(--foreground) / 0.95) !important;
}

.high-contrast-mode .ProseMirror strong {
  color: hsl(var(--foreground)) !important;
  font-weight: 800 !important;
}

.high-contrast-mode .ProseMirror em {
  color: hsl(var(--foreground) / 0.9) !important;
  font-style: italic !important;
}

/* Dark mode enhanced contrast */
.dark.high-contrast-mode .ProseMirror {
  background: #000000 !important;
  color: #ffffff !important;
}

.dark.high-contrast-mode .ProseMirror h1,
.dark.high-contrast-mode .ProseMirror h2,
.dark.high-contrast-mode .ProseMirror h3 {
  color: #ffffff !important;
}

.dark.high-contrast-mode .ProseMirror p {
  color: #f0f0f0 !important;
}

/* ===== TYPEWRITER MODE ===== */

.typewriter-mode .ProseMirror {
  padding-top: 50vh !important;
  padding-bottom: 50vh !important;
}

.typewriter-mode .ProseMirror p {
  opacity: 0.3 !important;
  transition: opacity 0.3s ease !important;
}

.typewriter-mode .ProseMirror p:has(.ProseMirror-focused),
.typewriter-mode .ProseMirror p.has-focus {
  opacity: 1 !important;
}

/* Fallback for browsers that don't support :has() */
.typewriter-mode .ProseMirror p.current-paragraph {
  opacity: 1 !important;
}

/* ===== READING MODE ===== */

.reading-mode .ProseMirror {
  font-family: "Georgia", "Times New Roman", serif !important;
  font-size: 1.2rem !important;
  line-height: 1.8 !important;
  max-width: 700px !important;
  margin: 0 auto !important;
  padding: 3rem 2rem !important;
  color: hsl(var(--foreground) / 0.9) !important;
}

.reading-mode .ProseMirror h1 {
  font-size: 2.5rem !important;
  margin: 2rem 0 1.5rem !important;
  text-align: center !important;
}

.reading-mode .ProseMirror h2 {
  font-size: 2rem !important;
  margin: 1.75rem 0 1rem !important;
}

.reading-mode .ProseMirror h3 {
  font-size: 1.5rem !important;
  margin: 1.5rem 0 0.75rem !important;
}

.reading-mode .ProseMirror p {
  margin-bottom: 1.5rem !important;
  text-align: justify !important;
}

/* ===== DISTRACTION-FREE MODE ===== */

.distraction-free-mode .editor-header,
.distraction-free-mode .editor-footer,
.distraction-free-mode .editor-tabs {
  opacity: 0 !important;
  pointer-events: none !important;
  transition: opacity 0.3s ease !important;
}

.distraction-free-mode:hover .editor-header,
.distraction-free-mode:hover .editor-footer,
.distraction-free-mode:hover .editor-tabs {
  opacity: 1 !important;
  pointer-events: auto !important;
}

.distraction-free-mode .ProseMirror {
  padding: 4rem 3rem !important;
  min-height: 80vh !important;
}

/* ===== ENHANCED SELECTION HIGHLIGHTING ===== */

.enhanced-selection .ProseMirror ::selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3), 
    hsl(var(--primary) / 0.2)) !important;
  color: hsl(var(--primary-foreground)) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.enhanced-selection .ProseMirror ::-moz-selection {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3), 
    hsl(var(--primary) / 0.2)) !important;
  color: hsl(var(--primary-foreground)) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* ===== WRITING STATISTICS OVERLAY ===== */

.writing-stats-overlay {
  position: fixed !important;
  top: 50% !important;
  right: 2rem !important;
  transform: translateY(-50%) !important;
  background: hsl(var(--background) / 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid hsl(var(--border) / 0.4) !important;
  border-radius: 16px !important;
  padding: 1.5rem !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(0, 0, 0, 0.05) !important;
  z-index: 1000 !important;
  min-width: 200px !important;
  opacity: 0 !important;
  pointer-events: none !important;
  transition: all 0.3s ease !important;
}

.writing-stats-overlay.visible {
  opacity: 1 !important;
  pointer-events: auto !important;
}

.writing-stats-overlay h3 {
  font-size: 1rem !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
  color: hsl(var(--foreground)) !important;
}

.writing-stats-overlay .stat-item {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 0.75rem !important;
  font-size: 0.875rem !important;
}

.writing-stats-overlay .stat-label {
  color: hsl(var(--muted-foreground)) !important;
  font-weight: 500 !important;
}

.writing-stats-overlay .stat-value {
  color: hsl(var(--primary)) !important;
  font-weight: 700 !important;
}

/* ===== ENHANCED CURSOR ===== */

.enhanced-cursor .ProseMirror {
  caret-color: hsl(var(--primary)) !important;
}

.enhanced-cursor .ProseMirror:focus {
  caret-color: hsl(var(--primary)) !important;
}

/* Blinking cursor animation */
@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.enhanced-cursor .ProseMirror .ProseMirror-cursor {
  animation: cursor-blink 1.2s infinite !important;
  border-left: 2px solid hsl(var(--primary)) !important;
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */

@media (max-width: 768px) {
  .editor-focus-mode {
    padding: 1rem !important;
  }
  
  .editor-focus-mode .ProseMirror {
    padding: 2rem 1rem !important;
    font-size: 1.125rem !important;
  }
  
  .focus-mode-toggle {
    top: 1rem !important;
    right: 1rem !important;
  }
  
  .writing-stats-overlay {
    right: 1rem !important;
    min-width: 180px !important;
    padding: 1rem !important;
  }
  
  .reading-mode .ProseMirror {
    padding: 2rem 1rem !important;
    font-size: 1.1rem !important;
  }
}

@media (max-width: 480px) {
  .editor-focus-mode .ProseMirror {
    padding: 1.5rem 0.75rem !important;
    font-size: 1rem !important;
  }
  
  .writing-stats-overlay {
    position: relative !important;
    top: auto !important;
    right: auto !important;
    transform: none !important;
    margin: 1rem 0 !important;
    width: 100% !important;
  }
}
