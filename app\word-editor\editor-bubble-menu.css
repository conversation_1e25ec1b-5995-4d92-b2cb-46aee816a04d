/* 
   This CSS file specifically targets the bubble menu that appears when text is selected 
   It applies more compact styling to make the menu less bulky
*/

/* Base bubble menu styling */
.bubble-menu {
  padding: 0.375rem !important; /* Slightly more padding for better touch targets */
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  z-index: 1000 !important; /* Higher z-index to ensure visibility */
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  animation: bubbleMenuFadeIn 0.15s ease-out !important;
}

@keyframes bubbleMenuFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Improved styling for bubble menu buttons */
.bubble-menu button {
  padding: 0.375rem 0.625rem !important; /* Better padding for touch targets */
  gap: 0.375rem !important; /* Adequate gap between icon and text */
  font-size: 0.875rem !important; /* Readable font size */
  border-radius: 0.375rem !important;
  min-height: unset !important;
  height: 32px !important; /* Slightly larger for better usability */
  transition: all 0.15s ease !important;
  border: 1px solid transparent !important;
}

.bubble-menu button:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-color: rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

.bubble-menu button:active {
  transform: translateY(0) !important;
}

.bubble-menu button[data-state="on"] {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  color: rgb(59, 130, 246) !important;
}

/* Ensure icons are properly sized */
.bubble-menu button svg {
  width: 14px !important;
  height: 14px !important;
}

/* For compatibility with existing code that might use these classes */
.selection-menu {
  padding: 0.25rem; /* Reduced padding */
  border-radius: 0.375rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.selection-menu-button {
  padding: 0.25rem 0.5rem; /* Reduced padding */
  gap: 0.25rem; /* Reduced gap */
  font-size: 0.875rem; /* Smaller font size */
  height: 28px; /* Fixed smaller height */
}

/* Dark mode adjustments */
.dark .bubble-menu {
  background-color: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

.dark .bubble-menu button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.dark .bubble-menu button[data-state="on"] {
  background-color: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: rgb(147, 197, 253) !important;
}

/* Improved separator styling */
.bubble-menu .mx-1 {
  background-color: rgba(0, 0, 0, 0.1) !important;
  margin: 0 0.5rem !important;
}

.dark .bubble-menu .mx-1 {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
