/*
   Premium Bubble Menu Styling for Tersa Word Editor
   Ultra-modern design with smooth animations, glass-morphism, and enhanced UX
*/

/* ===== PREMIUM BUBBLE MENU CONTAINER ===== */

.bubble-menu {
  /* Enhanced spacing and layout */
  padding: 0.875rem !important;
  border-radius: 1.5rem !important;
  min-height: 48px !important;
  
  /* Advanced glass-morphism effect */
  backdrop-filter: blur(32px) saturate(200%) brightness(110%) !important;
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.98), 
    hsl(var(--background) / 0.92)) !important;
  
  /* Premium shadow system */
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.12),
    0 16px 40px rgba(0, 0, 0, 0.08),
    0 8px 20px rgba(0, 0, 0, 0.06),
    0 4px 10px rgba(0, 0, 0, 0.04),
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05) !important;
  
  /* Enhanced border */
  border: 1px solid hsl(var(--border) / 0.3) !important;
  
  /* Smooth animations */
  animation: bubbleSlideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  transform-origin: bottom center;
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  
  /* Layout and positioning */
  position: relative;
  z-index: 9999;
  max-width: none;
  overflow: visible;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Dark mode enhancements */
.dark .bubble-menu {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.88)) !important;
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.4),
    0 16px 40px rgba(0, 0, 0, 0.3),
    0 8px 20px rgba(0, 0, 0, 0.25),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
  border: 1px solid hsl(var(--border) / 0.6) !important;
}

/* ===== ENHANCED ANIMATIONS ===== */

@keyframes bubbleSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.85) translateY(12px) rotateX(15deg);
    filter: blur(8px);
  }
  50% {
    opacity: 0.8;
    transform: scale(0.95) translateY(4px) rotateX(5deg);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
    filter: blur(0);
  }
}

@keyframes bubbleSlideOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translateY(8px);
    filter: blur(4px);
  }
}

/* ===== PREMIUM BUTTON STYLING ===== */

.bubble-menu button {
  /* Enhanced spacing and sizing */
  padding: 0.625rem 0.875rem !important;
  gap: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  height: 40px !important;
  min-width: 40px !important;
  border-radius: 1rem !important;
  
  /* Advanced styling */
  position: relative;
  overflow: hidden;
  background-color: transparent !important;
  color: hsl(var(--foreground) / 0.85) !important;
  border: 1px solid transparent !important;
  backdrop-filter: blur(8px);
  
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  
  /* Typography */
  letter-spacing: -0.01em;
  text-rendering: optimizeLegibility;
}

/* Enhanced hover effects */
.bubble-menu button:hover {
  background: linear-gradient(135deg, 
    hsl(var(--accent) / 0.15), 
    hsl(var(--accent) / 0.08)) !important;
  border-color: hsl(var(--accent) / 0.4) !important;
  transform: translateY(-2px) scale(1.03);
  color: hsl(var(--accent-foreground)) !important;
  box-shadow: 
    0 8px 25px hsl(var(--accent) / 0.25), 
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced active/pressed state */
.bubble-menu button[data-state="on"],
.bubble-menu button.active {
  background: linear-gradient(135deg, 
    hsl(var(--accent)), 
    hsl(var(--accent) / 0.9)) !important;
  border-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
  box-shadow: 
    0 8px 25px hsl(var(--accent) / 0.4), 
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-1px) scale(1.02);
}

/* Button press animation */
.bubble-menu button:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

/* ===== ENHANCED ICONS ===== */

.bubble-menu button svg {
  width: 18px !important;
  height: 18px !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.bubble-menu button:hover svg {
  transform: scale(1.15) rotate(2deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
}

.bubble-menu button[data-state="on"] svg,
.bubble-menu button.active svg {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== ENHANCED SEPARATORS ===== */

.bubble-menu-divider,
.bubble-menu .h-4.w-px.bg-border,
.bubble-menu [role="separator"] {
  height: 28px !important;
  width: 1px !important;
  opacity: 0.3;
  margin: 0 0.5rem !important;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    hsl(var(--border) / 0.6) 20%,
    hsl(var(--border) / 0.8) 50%,
    hsl(var(--border) / 0.6) 80%,
    transparent 100%
  ) !important;
  border-radius: 1px;
  transition: opacity 0.3s ease;
}

/* ===== BUBBLE MENU SECTIONS ===== */

.bubble-menu-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.bubble-menu-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* ===== AI BUTTON SPECIAL STYLING ===== */

.bubble-menu-ai-button {
  background: linear-gradient(135deg, 
    hsl(280, 100%, 70%), 
    hsl(260, 100%, 65%)) !important;
  color: white !important;
  border: none !important;
  font-size: 1rem !important;
  animation: aiGlow 2s ease-in-out infinite alternate;
}

.bubble-menu-ai-button:hover {
  background: linear-gradient(135deg, 
    hsl(280, 100%, 75%), 
    hsl(260, 100%, 70%)) !important;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 12px 30px hsl(270, 100%, 70% / 0.4), 
    0 6px 15px rgba(0, 0, 0, 0.2);
}

@keyframes aiGlow {
  0% {
    box-shadow: 0 0 20px hsl(270, 100%, 70% / 0.3);
  }
  100% {
    box-shadow: 0 0 30px hsl(270, 100%, 70% / 0.5);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

.bubble-menu button:focus-visible {
  outline: 3px solid hsl(var(--primary)) !important;
  outline-offset: 3px !important;
  background-color: hsl(var(--accent) / 0.15) !important;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .bubble-menu {
    padding: 0.625rem !important;
    border-radius: 1.25rem !important;
    gap: 0.125rem;
  }

  .bubble-menu button {
    padding: 0.5rem 0.625rem !important;
    height: 36px !important;
    min-width: 36px !important;
    font-size: 0.8rem !important;
    border-radius: 0.875rem !important;
  }

  .bubble-menu button svg {
    width: 16px !important;
    height: 16px !important;
  }

  .bubble-menu-divider {
    margin: 0 0.25rem !important;
    height: 24px !important;
  }
}

@media (max-width: 480px) {
  .bubble-menu {
    padding: 0.5rem !important;
    border-radius: 1rem !important;
  }

  .bubble-menu button {
    padding: 0.375rem 0.5rem !important;
    height: 32px !important;
    min-width: 32px !important;
    font-size: 0.75rem !important;
  }

  .bubble-menu button svg {
    width: 14px !important;
    height: 14px !important;
  }
}
