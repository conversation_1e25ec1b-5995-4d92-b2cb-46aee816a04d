/* Additional Styles to beautify the editor and improve readability */

/* Enhanced editor styling */
.ProseMirror {
  padding: 1.5rem;
  outline: none !important;
  transition: background-color 0.3s ease, box-shadow 0.3s ease !important;
  position: relative;
  border-radius: 8px;
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  line-height: 1.8;
  font-size: 1.05rem;
  letter-spacing: -0.011em;
  background-color: hsla(var(--background) / 0.6) !important;
}

/* Better focus effect */
.ProseMirror:focus {
  background-color: hsla(var(--background) / 1) !important;
  box-shadow: inset 0 0 0 1px hsl(var(--primary) / 0.2), 0 0 0 3px
    hsl(var(--primary) / 0.1);
}

/* Improved text selection */
.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.15) !important;
  color: inherit !important;
}

/* Better cursor visibility */
.ProseMirror .ProseMirror-cursor {
  border-left: 2px solid hsl(var(--primary) / 0.8) !important;
  animation: blink 1.2s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Create beautiful paper effect */
.editor-paper {
  background: linear-gradient(
    to right,
    hsla(var(--muted) / 0.05),
    hsla(var(--background) / 1) 10%,
    hsla(var(--background) / 1) 90%,
    hsla(var(--muted) / 0.05)
  );
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.03);
  border-radius: 12px !important;
  border: 1px solid hsla(var(--border) / 0.7) !important;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 6px 16px rgba(0, 0, 0, 0.05);
}

.dark .editor-paper {
  --paper-bg: rgba(30, 41, 59, 0.8);
  --paper-line-color: rgba(255, 255, 255, 0.04);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* Selection styling */
.ProseMirror ::selection {
  background-color: rgba(59, 130, 246, 0.3); /* blue-500 with opacity */
  color: inherit !important; /* Ensure text remains readable */
  text-shadow: none !important;
  border-radius: 2px;
}

.dark .ProseMirror ::selection {
  background-color: rgba(96, 165, 250, 0.4); /* blue-400 with opacity */
  color: white !important; /* Better contrast in dark mode */
}

/* Improved selection styles by element type */
.ProseMirror h1::selection,
.ProseMirror h1 *::selection {
  background-color: rgba(59, 130, 246, 0.4); /* More opaque for headings */
}

.ProseMirror h2::selection,
.ProseMirror h2 *::selection {
  background-color: rgba(147, 51, 234, 0.35); /* Purple tint for h2 */
}

.ProseMirror h3::selection,
.ProseMirror h3 *::selection {
  background-color: rgba(236, 72, 153, 0.35); /* Pink tint for h3 */
}

/* Preserve styles during selection */
.ProseMirror h1.is-selected,
.ProseMirror h2.is-selected,
.ProseMirror h3.is-selected,
.ProseMirror h4.is-selected,
.ProseMirror h5.is-selected,
.ProseMirror h6.is-selected {
  background-color: rgba(59, 130, 246, 0.1); /* Subtle highlight for entire heading block */
  border-radius: 4px;
}

/* Bubble menu enhancements */
.bubble-menu {
  border-radius: 12px;
  padding: 4px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08), 0 8px 20px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, opacity 0.2s ease;
  animation: fadeIn 0.15s ease-out;
  transform-origin: center bottom;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.bubble-menu button {
  transition: all 0.15s ease;
  border-radius: 8px;
  margin: 2px;
  padding: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bubble-menu button svg {
  width: 16px;
  height: 16px;
}

.bubble-menu button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.bubble-menu button[data-active="true"] {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.dark .bubble-menu {
  background-color: rgba(30, 41, 59, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .bubble-menu button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .bubble-menu button[data-active="true"] {
  background-color: rgba(96, 165, 250, 0.2);
  color: rgb(96, 165, 250);
}

/* Enhance floating menu */
.floating-menu {
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06), 0 8px 30px rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.25s ease-out;
  z-index: 100;
  padding: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.floating-menu button {
  border-radius: 8px;
  padding: 8px !important;
  transition: all 0.15s ease;
}

.floating-menu button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.dark .floating-menu {
  background-color: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .floating-menu button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Better placeholder styling */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: rgba(115, 115, 115, 0.5);
  pointer-events: none;
  height: 0;
  font-style: italic;
  letter-spacing: 0.01em;
  opacity: 0.8;
}

.dark .ProseMirror p.is-editor-empty:first-child::before {
  color: rgba(190, 190, 190, 0.5);
}

/* Better typography for editor */
.ProseMirror h1 {
  font-size: 2.4rem;
  font-weight: 800;
  margin-top: 2rem;
  margin-bottom: 1.2rem;
  color: var(--primary);
  letter-spacing: -0.03em;
  line-height: 1.2;
  font-family: var(--font-display, var(--font-sans));
  position: relative;
}

.ProseMirror h1::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), transparent);
  border-radius: 2px;
  opacity: 0.6;
}

.ProseMirror h2 {
  font-size: 1.85rem;
  font-weight: 700;
  margin-top: 1.8rem;
  margin-bottom: 0.9rem;
  color: var(--primary);
  letter-spacing: -0.02em;
  line-height: 1.3;
  font-family: var(--font-display, var(--font-sans));
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.8rem;
  letter-spacing: -0.015em;
  line-height: 1.4;
  font-family: var(--font-display, var(--font-sans));
}

.ProseMirror p {
  margin-bottom: 1.2rem;
  line-height: 1.8;
  color: var(--foreground);
}

.ProseMirror blockquote {
  border-left: 3px solid var(--primary);
  padding-left: 1rem;
  margin-left: 0;
  font-style: italic;
  color: var(--muted-foreground);
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
  margin-bottom: 1.2rem;
}

.ProseMirror li {
  margin-bottom: 0.5rem;
}

.ProseMirror a {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
  transition: all 0.2s ease;
}

.ProseMirror a:hover {
  text-decoration-thickness: 2px;
  color: var(--primary-dark, var(--primary));
}

.ProseMirror pre {
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-family: monospace;
  font-size: 0.9rem;
  overflow-x: auto;
}

.dark .ProseMirror pre {
  background-color: rgba(255, 255, 255, 0.05);
}
