/* 
   Premium Enhancements for Tersa Word Editor
   Comprehensive styling improvements for a professional, modern editor experience
*/

/* ===== EDITOR CONTAINER & LAYOUT ===== */

/* Enhanced editor card with premium glass-morphism effect */
.editor-card {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.95),
    hsl(var(--background) / 0.9)
  );
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 0 8px 25px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

.dark .editor-card {
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.98),
    hsl(var(--background) / 0.95)
  );
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 25px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Enhanced editor header */
.editor-header {
  background: linear-gradient(
    135deg,
    hsl(var(--muted) / 0.3),
    hsl(var(--muted) / 0.1)
  );
  border-bottom: 1px solid hsl(var(--border) / 0.6);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
}

/* Premium editor title styling */
.editor-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    hsl(var(--foreground)),
    hsl(var(--primary))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

/* Enhanced action buttons */
.editor-action-button {
  backdrop-filter: blur(8px);
  background-color: hsl(var(--background) / 0.8);
  border: 1px solid hsl(var(--border) / 0.6);
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  font-weight: 500;
}

.editor-action-button:hover {
  background-color: hsl(var(--accent) / 0.15);
  border-color: hsl(var(--accent) / 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--accent) / 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== EDITOR TABS ===== */

.editor-tabs {
  background: linear-gradient(
    135deg,
    hsl(var(--muted) / 0.2),
    hsl(var(--muted) / 0.05)
  );
  border-radius: 12px;
  padding: 0.25rem;
  margin: 0.5rem;
}

.editor-tab {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.editor-tab[data-state="active"] {
  background: linear-gradient(
    135deg,
    hsl(var(--background)),
    hsl(var(--background) / 0.95)
  );
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0
    rgba(255, 255, 255, 0.2);
}

/* ===== EDITOR CONTENT AREA ===== */

/* Enhanced paper effect for editor content */
.editor-paper {
  background: linear-gradient(
    135deg,
    hsl(var(--background)),
    hsl(var(--background) / 0.98)
  );
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.editor-paper::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: radial-gradient(
    circle at 2px 2px,
    hsl(var(--muted) / 0.1) 1px,
    transparent 0
  );
  background-size: 24px 24px;
  pointer-events: none;
  opacity: 0.4;
}

/* ===== SELECTION ENHANCEMENTS ===== */

/* Enhanced text selection styling */
.selection-editor ::selection {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.25),
    hsl(var(--primary) / 0.15)
  );
  color: hsl(var(--foreground));
  border-radius: 3px;
  text-shadow: none;
}

.selection-editor:focus ::selection {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.35),
    hsl(var(--primary) / 0.25)
  );
}

/* ===== ENHANCED BUBBLE MENU ===== */

/* Enhanced bubble menu container */
.bubble-menu-enhanced {
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.98),
    hsl(var(--background) / 0.95)
  );
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid hsl(var(--border) / 0.6);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  z-index: 9999;
  animation: bubble-menu-in 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.dark .bubble-menu-enhanced {
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.95),
    hsl(var(--background) / 0.9)
  );
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Bubble menu content layout */
.bubble-menu-content {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.bubble-menu-section {
  display: flex;
  align-items: center;
  gap: 0.125rem;
}

.bubble-menu-divider {
  width: 1px;
  height: 1.5rem;
  background: linear-gradient(
    to bottom,
    transparent,
    hsl(var(--border) / 0.6),
    transparent
  );
  margin: 0 0.25rem;
}

/* Enhanced bubble menu buttons */
.bubble-menu-enhanced button {
  border-radius: 6px;
  transition: all 0.15s cubic-bezier(0.16, 1, 0.3, 1);
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.375rem 0.5rem;
  min-width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bubble-menu-enhanced button:hover {
  background: hsl(var(--accent) / 0.15);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px hsl(var(--accent) / 0.2);
}

.bubble-menu-enhanced button[data-state="on"] {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.15),
    hsl(var(--primary) / 0.1)
  );
  color: hsl(var(--primary));
  border: 1px solid hsl(var(--primary) / 0.3);
}

/* AI button special styling */
.bubble-menu-ai-button {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.1),
    hsl(var(--primary) / 0.05)
  );
  border: 1px solid hsl(var(--primary) / 0.2);
  color: hsl(var(--primary));
  font-size: 1rem;
}

.bubble-menu-ai-button:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.2),
    hsl(var(--primary) / 0.15)
  );
  border-color: hsl(var(--primary) / 0.4);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
}

/* Bubble menu animations */
@keyframes bubble-menu-in {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* ===== AI TOOLBAR ENHANCEMENTS ===== */

/* AI toolbar button styling */
.ai-toolbar-button {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.08),
    hsl(var(--primary) / 0.04)
  );
  border: 1px solid hsl(var(--primary) / 0.15);
  color: hsl(var(--primary));
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.ai-toolbar-button::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.1), transparent);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-toolbar-button:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 0.15),
    hsl(var(--primary) / 0.08)
  );
  border-color: hsl(var(--primary) / 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-toolbar-button:hover::before {
  opacity: 1;
}

.ai-toolbar-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px hsl(var(--primary) / 0.15), 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* AI toolbar button sparkle animation */
.ai-toolbar-button svg {
  transition: all 0.2s ease;
}

.ai-toolbar-button:hover svg {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 4px hsl(var(--primary) / 0.5));
}

/* Enhanced writing experience improvements */
.prose-writer {
  font-family: "Georgia", "Times New Roman", serif;
  font-size: 1.1rem;
  line-height: 1.7;
  color: hsl(var(--foreground) / 0.9);
  letter-spacing: 0.01em;
}

.prose-writer p {
  margin-bottom: 1.25em;
  text-align: justify;
  hyphens: auto;
}

.prose-writer h1,
.prose-writer h2,
.prose-writer h3 {
  font-family: "Inter", "Helvetica Neue", sans-serif;
  font-weight: 600;
  letter-spacing: -0.02em;
  color: hsl(var(--foreground));
}

.prose-writer h1 {
  font-size: 2.25rem;
  margin-bottom: 1rem;
  margin-top: 2rem;
}

.prose-writer h2 {
  font-size: 1.875rem;
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
}

.prose-writer h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  margin-top: 1.25rem;
}

.prose-writer blockquote {
  border-left: 4px solid hsl(var(--primary) / 0.3);
  padding-left: 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.prose-writer ul,
.prose-writer ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.prose-writer li {
  margin-bottom: 0.5rem;
}

.prose-writer code {
  background: hsl(var(--muted) / 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
  font-family: "JetBrains Mono", "Fira Code", monospace;
}

.prose-writer pre {
  background: hsl(var(--muted) / 0.3);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.prose-writer pre code {
  background: none;
  padding: 0;
}

/* Focus mode enhancements */
.editor-fullscreen .prose-writer {
  max-width: 65ch;
  margin: 0 auto;
  padding: 2rem;
}

.editor-fullscreen .prose-writer p {
  font-size: 1.2rem;
  line-height: 1.8;
}

/* ===== ENHANCED EDITOR CONTENT ===== */

.enhanced-editor-content {
  padding: 2rem;
  background: linear-gradient(
    135deg,
    hsl(var(--background)),
    hsl(var(--background) / 0.98)
  );
  border-radius: 12px;
  position: relative;
  min-height: 600px;
}

.enhanced-editor-content::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: radial-gradient(
    circle at 2px 2px,
    hsl(var(--muted) / 0.08) 1px,
    transparent 0
  );
  background-size: 24px 24px;
  pointer-events: none;
  opacity: 0.3;
  border-radius: 12px;
}

.enhanced-editor-content .ProseMirror {
  outline: none;
  position: relative;
  z-index: 1;
}

.enhanced-editor-content .ProseMirror:focus {
  outline: none;
}

/* Enhanced placeholder styling */
.enhanced-editor-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground) / 0.6);
  pointer-events: none;
  height: 0;
  font-style: italic;
  font-size: 1.1rem;
}

/* Better contrast for text */
.enhanced-editor-content .ProseMirror {
  color: hsl(var(--foreground) / 0.95);
}

.dark .enhanced-editor-content .ProseMirror {
  color: hsl(var(--foreground) / 0.9);
}

/* Enhanced heading styles */
.enhanced-editor-content .ProseMirror h1 {
  position: relative;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.enhanced-editor-content .ProseMirror h1::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.3),
    transparent
  );
  border-radius: 1px;
}

.enhanced-editor-content .ProseMirror h2 {
  position: relative;
  padding-left: 1rem;
  margin-bottom: 1rem;
}

.enhanced-editor-content .ProseMirror h2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  width: 4px;
  height: calc(100% - 0.5rem);
  background: linear-gradient(
    180deg,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.5)
  );
  border-radius: 2px;
}

.enhanced-editor-content .ProseMirror h3 {
  color: hsl(var(--primary));
  font-weight: 600;
}

/* Enhanced list styling */
.enhanced-editor-content .ProseMirror ul,
.enhanced-editor-content .ProseMirror ol {
  padding-left: 1.5rem;
}

.enhanced-editor-content .ProseMirror ul li::marker {
  color: hsl(var(--primary));
}

.enhanced-editor-content .ProseMirror ol li::marker {
  color: hsl(var(--primary));
  font-weight: 600;
}

/* Enhanced blockquote styling */
.enhanced-editor-content .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--primary) / 0.4);
  background: linear-gradient(
    90deg,
    hsl(var(--muted) / 0.3),
    hsl(var(--muted) / 0.1)
  );
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  position: relative;
}

.enhanced-editor-content .ProseMirror blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: hsl(var(--primary) / 0.3);
  font-family: serif;
  line-height: 1;
}

/* Enhanced code styling */
.enhanced-editor-content .ProseMirror code {
  background: hsl(var(--muted) / 0.6);
  color: hsl(var(--primary));
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.9em;
  font-weight: 500;
  border: 1px solid hsl(var(--border) / 0.5);
}

.enhanced-editor-content .ProseMirror pre {
  background: hsl(var(--muted) / 0.4);
  border: 1px solid hsl(var(--border) / 0.6);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  position: relative;
}

.enhanced-editor-content .ProseMirror pre::before {
  content: "";
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: hsl(var(--destructive));
  box-shadow: 20px 0 0 hsl(var(--warning)), 40px 0 0 hsl(var(--success));
}

.enhanced-editor-content .ProseMirror pre code {
  background: none;
  border: none;
  padding: 0;
  color: hsl(var(--foreground));
}

/* Enhanced table styling */
.enhanced-editor-content .ProseMirror table {
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  overflow: hidden;
  margin: 1.5rem 0;
  width: 100%;
}

.enhanced-editor-content .ProseMirror th {
  background: linear-gradient(
    135deg,
    hsl(var(--muted) / 0.8),
    hsl(var(--muted) / 0.6)
  );
  font-weight: 600;
  color: hsl(var(--foreground));
}

.enhanced-editor-content .ProseMirror th,
.enhanced-editor-content .ProseMirror td {
  border-right: 1px solid hsl(var(--border));
  border-bottom: 1px solid hsl(var(--border));
  padding: 0.75rem 1rem;
  text-align: left;
}

.enhanced-editor-content .ProseMirror th:last-child,
.enhanced-editor-content .ProseMirror td:last-child {
  border-right: none;
}

.enhanced-editor-content .ProseMirror tr:last-child td {
  border-bottom: none;
}

/* ===== EDITOR FOOTER ===== */

.editor-footer {
  background: linear-gradient(
    135deg,
    hsl(var(--muted) / 0.2),
    hsl(var(--muted) / 0.05)
  );
  border-top: 1px solid hsl(var(--border) / 0.6);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
}

/* Enhanced stats counters */
.stats-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: hsl(var(--background) / 0.8);
  border: 1px solid hsl(var(--border) / 0.4);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  transition: all 0.25s ease;
}

.stats-counter:hover {
  background: hsl(var(--accent) / 0.1);
  border-color: hsl(var(--accent) / 0.3);
  transform: translateY(-1px);
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.stats-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: hsl(var(--foreground));
}

/* ===== ANIMATIONS ===== */

/* Enhanced editor entrance animation */
@keyframes editor-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.animate-editor-in {
  animation: editor-in 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .editor-header {
    padding: 1rem 1.5rem;
  }

  .editor-title {
    font-size: 1.25rem;
  }

  .editor-footer {
    padding: 0.75rem 1.5rem;
  }

  .stats-counter {
    padding: 0.375rem 0.75rem;
  }

  .stats-label,
  .stats-value {
    font-size: 0.8rem;
  }
}

@media (max-width: 640px) {
  .editor-card {
    border-radius: 12px;
    margin: 0.5rem;
  }

  .editor-header {
    padding: 0.75rem 1rem;
  }

  .editor-footer {
    padding: 0.5rem 1rem;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Enhanced focus indicators */
.editor-card *:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .editor-card,
  .editor-action-button,
  .editor-tab,
  .stats-counter {
    transition: none;
  }

  .animate-editor-in {
    animation: none;
  }

  .ProseMirror h1::after {
    animation: none;
  }
}
