/*
   Ultra-Modern Editor Styling for Tersa Word Editor
   Enhanced typography, spacing, colors, and modern UI elements
*/

/* ===== ENHANCED EDITOR CONTAINER ===== */

.editor-card {
  border-radius: 20px !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.98), 
    hsl(var(--background) / 0.95)) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 1px solid hsl(var(--border) / 0.4) !important;
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.08),
    0 16px 40px rgba(0, 0, 0, 0.05),
    0 8px 20px rgba(0, 0, 0, 0.03),
    inset 0 2px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1) !important;
  position: relative;
}

.dark .editor-card {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9)) !important;
  border: 1px solid hsl(var(--border) / 0.6) !important;
  box-shadow: 
    0 32px 80px rgba(0, 0, 0, 0.25),
    0 16px 40px rgba(0, 0, 0, 0.15),
    0 8px 20px rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.05) !important;
}

/* ===== ENHANCED EDITOR HEADER ===== */

.editor-header {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9)) !important;
  border-bottom: 1px solid hsl(var(--border) / 0.3) !important;
  padding: 1.5rem !important;
  backdrop-filter: blur(10px) !important;
}

.editor-title {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  letter-spacing: -0.025em !important;
  color: hsl(var(--foreground) / 0.95) !important;
  text-rendering: optimizeLegibility !important;
  transition: all 0.3s ease !important;
}

.editor-title:hover {
  color: hsl(var(--primary)) !important;
  transform: translateY(-1px) !important;
}

/* ===== ENHANCED ACTION BUTTONS ===== */

.editor-action-button {
  padding: 0.75rem 1rem !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  letter-spacing: -0.01em !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.8), 
    hsl(var(--background) / 0.6)) !important;
  border: 1px solid hsl(var(--border) / 0.4) !important;
  backdrop-filter: blur(8px) !important;
  position: relative;
  overflow: hidden;
}

.editor-action-button:hover {
  background: linear-gradient(135deg, 
    hsl(var(--accent) / 0.15), 
    hsl(var(--accent) / 0.08)) !important;
  border-color: hsl(var(--accent) / 0.4) !important;
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 
    0 12px 30px hsl(var(--accent) / 0.2),
    0 6px 15px rgba(0, 0, 0, 0.1) !important;
}

.editor-action-button:active {
  transform: translateY(0) scale(0.98) !important;
  transition: all 0.1s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

/* ===== ENHANCED TABS ===== */

.editor-tabs {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.9), 
    hsl(var(--background) / 0.8)) !important;
  border-radius: 16px !important;
  padding: 0.5rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

.editor-tab {
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  letter-spacing: -0.01em !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  position: relative;
  overflow: hidden;
}

.editor-tab[data-state="active"] {
  background: linear-gradient(135deg, 
    hsl(var(--primary)), 
    hsl(var(--primary) / 0.9)) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow: 
    0 8px 25px hsl(var(--primary) / 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.editor-tab:hover:not([data-state="active"]) {
  background: hsl(var(--accent) / 0.1) !important;
  transform: translateY(-1px) !important;
}

/* ===== ENHANCED PROSEMIRROR EDITOR ===== */

.ProseMirror {
  padding: 2rem !important;
  outline: none !important;
  transition: all 0.3s ease !important;
  position: relative;
  border-radius: 16px !important;
  font-family: "Inter", "SF Pro Text", system-ui, -apple-system, sans-serif !important;
  line-height: 1.75 !important;
  font-size: 1.125rem !important;
  letter-spacing: -0.015em !important;
  color: hsl(var(--foreground) / 0.9) !important;
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.7), 
    hsl(var(--background) / 0.5)) !important;
  backdrop-filter: blur(8px) !important;
  min-height: 600px !important;
}

.ProseMirror:focus {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9)) !important;
  box-shadow: 
    inset 0 0 0 2px hsl(var(--primary) / 0.2),
    0 0 0 4px hsl(var(--primary) / 0.1),
    0 8px 25px rgba(0, 0, 0, 0.05) !important;
  transform: scale(1.002) !important;
}

/* ===== ENHANCED TYPOGRAPHY ===== */

.ProseMirror h1 {
  font-size: 2.5rem !important;
  font-weight: 800 !important;
  line-height: 1.2 !important;
  margin: 2rem 0 1.5rem !important;
  letter-spacing: -0.04em !important;
  color: hsl(var(--foreground) / 0.95) !important;
  background: linear-gradient(135deg, 
    hsl(var(--primary)), 
    hsl(var(--primary) / 0.8)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  position: relative;
}

.ProseMirror h2 {
  font-size: 2rem !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  margin: 1.75rem 0 1rem !important;
  letter-spacing: -0.03em !important;
  color: hsl(var(--foreground) / 0.9) !important;
}

.ProseMirror h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 1.5rem 0 0.75rem !important;
  letter-spacing: -0.02em !important;
  color: hsl(var(--foreground) / 0.85) !important;
}

.ProseMirror p {
  margin-bottom: 1.25rem !important;
  line-height: 1.75 !important;
  color: hsl(var(--foreground) / 0.8) !important;
  text-rendering: optimizeLegibility !important;
}

.ProseMirror strong {
  font-weight: 700 !important;
  color: hsl(var(--foreground) / 0.95) !important;
}

.ProseMirror em {
  font-style: italic !important;
  color: hsl(var(--foreground) / 0.85) !important;
}

/* ===== ENHANCED LISTS ===== */

.ProseMirror ul,
.ProseMirror ol {
  margin: 1.5rem 0 !important;
  padding-left: 2rem !important;
}

.ProseMirror li {
  margin-bottom: 0.75rem !important;
  line-height: 1.7 !important;
  color: hsl(var(--foreground) / 0.8) !important;
}

.ProseMirror ul li::marker {
  color: hsl(var(--primary)) !important;
  font-size: 1.2em !important;
}

.ProseMirror ol li::marker {
  color: hsl(var(--primary)) !important;
  font-weight: 600 !important;
}

/* ===== ENHANCED BLOCKQUOTES ===== */

.ProseMirror blockquote {
  margin: 2rem 0 !important;
  padding: 1.5rem 2rem !important;
  border-left: 4px solid hsl(var(--primary)) !important;
  background: linear-gradient(135deg, 
    hsl(var(--accent) / 0.1), 
    hsl(var(--accent) / 0.05)) !important;
  border-radius: 0 12px 12px 0 !important;
  font-style: italic !important;
  font-size: 1.1rem !important;
  color: hsl(var(--foreground) / 0.8) !important;
  backdrop-filter: blur(8px) !important;
  position: relative;
}

.ProseMirror blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 4rem;
  color: hsl(var(--primary) / 0.3);
  font-family: serif;
  line-height: 1;
}

/* ===== ENHANCED CODE BLOCKS ===== */

.ProseMirror pre {
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.8), 
    hsl(var(--muted) / 0.6)) !important;
  border: 1px solid hsl(var(--border) / 0.4) !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin: 1.5rem 0 !important;
  overflow-x: auto !important;
  backdrop-filter: blur(8px) !important;
  font-family: "JetBrains Mono", "Fira Code", monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
}

.ProseMirror code {
  background: hsl(var(--muted) / 0.6) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 6px !important;
  padding: 0.25rem 0.5rem !important;
  font-family: "JetBrains Mono", "Fira Code", monospace !important;
  font-size: 0.875rem !important;
  color: hsl(var(--foreground) / 0.9) !important;
}

/* ===== ENHANCED FOOTER ===== */

.editor-footer {
  background: linear-gradient(135deg, 
    hsl(var(--background) / 0.95), 
    hsl(var(--background) / 0.9)) !important;
  border-top: 1px solid hsl(var(--border) / 0.3) !important;
  padding: 1.25rem 1.5rem !important;
  backdrop-filter: blur(10px) !important;
}

.stats-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: hsl(var(--muted) / 0.5);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--border) / 0.3);
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.stats-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: hsl(var(--primary));
}

/* ===== ENHANCED SELECTION STYLES ===== */

.ProseMirror ::selection {
  background: hsl(var(--primary) / 0.2) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.ProseMirror ::-moz-selection {
  background: hsl(var(--primary) / 0.2) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* ===== ENHANCED ANIMATIONS ===== */

.animate-editor-in {
  animation: editorSlideIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes editorSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    filter: blur(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */

@media (max-width: 768px) {
  .ProseMirror {
    padding: 1.5rem !important;
    font-size: 1rem !important;
  }
  
  .editor-title {
    font-size: 1.5rem !important;
  }
  
  .ProseMirror h1 {
    font-size: 2rem !important;
  }
  
  .ProseMirror h2 {
    font-size: 1.75rem !important;
  }
  
  .ProseMirror h3 {
    font-size: 1.25rem !important;
  }
}
