'use client';

// Kibo UI and Editor Components
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  EditorBubbleMenu,
  EditorClearFormatting,
  EditorFormatBold,
  EditorFormatCode,
  EditorFormatItalic,
  EditorFormatStrike,
  EditorFormatUnderline,
  EditorLinkSelector,
  EditorNodeBulletList,
  EditorNodeHeading1,
  EditorNodeHeading2,
  EditorNodeOrderedList,
  EditorProvider,
} from '@/components/ui/kibo-ui/editor';
import { useKiboEditor } from '@/hooks/use-kibo-editor';
import { setGlobalKiboEditor } from '@/lib/kibo-editor-global';
import type { Editor } from '@tiptap/core';
import { EditorContent } from '@tiptap/react';
import {
  Download,
  File,
  FileText,
  Maximize,
  Minimize,
  Save,
} from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

// Base and General Editor Styles (Import these first)
import './editor-enhancements.css';
import './editor-headings.css';
import './editor-additional-styles.css';
import './editor-bubble-compact.css';
import './editor-fullscreen.css';
import './editor-header-styles.css';
import './editor-layout-fixes.css';
import './editor-selection-styles.css';
import './editor-ai-commands.css';
import './editor-enhanced-menus.css';
import './editor-splash-screen.css';
import './editor-writing-experience.css';

// Enhanced and Modified Styles (Import these after base styles to allow overrides)
import './editor-bubble-compact-enhanced.css';
import './editor-modern-styles-enhanced.css';
import './editor-slash-commands-enhanced.css';
import './editor-enhanced-menus-improved.css';
import './editor-premium-enhancements.css';

// UI Components
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AITextDialog } from '@/components/word-editor/ai-text-dialog';
import { DocumentSelector } from '@/components/word-editor/document-selector';
import {
  EditorNodeHeading3,
  EditorNodeQuote,
  EditorNodeTaskList,
} from '@/components/word-editor/editor-additional-components';
import { EditorSplashScreen } from '@/components/word-editor/editor-splash-screen';
import { EditorToolbar } from '@/components/word-editor/editor-toolbar-enhanced';
import { EnhancedFloatingMenu } from '@/components/word-editor/enhanced-floating-menu';
import { EnhancedTableMenu } from '@/components/word-editor/enhanced-table-menu';
import { EditorHeader } from '@/components/word-editor/header';
import { SafeHTMLPreview } from '@/components/word-editor/safe-html-preview';
import type { StoredDocument } from '@/lib/document-storage';
import {
  generateDocumentId,
  saveDocumentToStorage,
} from '@/lib/document-storage';

// Declare global for TypeScript
declare global {
  interface Window {
    __KIBO_EDITOR_INSTANCE: Editor | null;
  }
}

export default function WordEditorPage() {
  const {
    document: doc,
    handleEditorUpdate,
    exportToPdf,
    exportToDocx,
    isFullscreen,
    toggleFullscreen,
    wordCount,
    charCount,
    extensions,
  } = useKiboEditor();
  const editorRef = useRef<Editor | null>(null);
  const [currentEditor, setCurrentEditor] = useState<Editor | null>(null);
  const [aiDialogOpen, setAiDialogOpen] = useState(false);
  const [documentId, setDocumentId] = useState<string | undefined>(undefined);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [editorReady, setEditorReady] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // Check for pending content from flow nodes
    try {
      const pendingContentStr = localStorage.getItem(
        'tersa_pending_word_editor_content'
      );
      if (pendingContentStr) {
        const pendingContent = JSON.parse(pendingContentStr);

        // Check if content is not too old (24 hours)
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        if (Date.now() - pendingContent.timestamp <= maxAge) {
          // Wait for editor to be ready, then add content
          setTimeout(() => {
            if (
              typeof window !== 'undefined' &&
              window.__KIBO_EDITOR_INSTANCE
            ) {
              const editor = window.__KIBO_EDITOR_INSTANCE;
              editor.commands.focus('end');
              const currentContent = editor.getHTML();
              if (currentContent && !currentContent.endsWith('<p></p>')) {
                editor.commands.insertContent('\n\n');
              }
              editor.commands.insertContent(pendingContent.content);
              localStorage.removeItem('tersa_pending_word_editor_content');

              // Show success message
              import('sonner').then(({ toast }) => {
                toast.success('Content from flow added to editor');
              });
            }
          }, 1000); // Wait 1 second for editor to be fully ready
        } else {
          // Remove old content
          localStorage.removeItem('tersa_pending_word_editor_content');
        }
      }
    } catch (error) {
      console.error('Error loading pending content:', error);
    }
  }, []);

  useEffect(() => {
    if (currentEditor) {
      setGlobalKiboEditor(currentEditor);
      if (typeof window !== 'undefined') {
        window.__KIBO_EDITOR_INSTANCE = currentEditor;
      }
    }
    return () => {
      setGlobalKiboEditor(null);
      if (typeof window !== 'undefined') {
        window.__KIBO_EDITOR_INSTANCE = null;
      }
    };
  }, [currentEditor]);

  const saveCurrentDocument = useCallback(() => {
    if (!doc) {
      return;
    }

    let currentDocId = documentId;
    if (!currentDocId) {
      currentDocId = generateDocumentId();
      setDocumentId(currentDocId);
    }

    const documentToSave: StoredDocument = {
      id: currentDocId,
      title: doc.title,
      content: doc.content,
      createdAt: doc.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      wordCount,
      charCount,
    };

    saveDocumentToStorage(documentToSave);
    setLastSaved(new Date());
  }, [doc, documentId, wordCount, charCount, setDocumentId]); // setDocumentId is included as it's called within

  const debouncedSaveDocument = useCallback(() => {
    if (doc.content && doc.content.length > 10) {
      saveCurrentDocument();
    }
  }, [doc.content, saveCurrentDocument]);

  useEffect(() => {
    const autoSaveTimeout = setTimeout(() => {
      debouncedSaveDocument();
    }, 5000); // Increased delay to reduce interruptions while writing

    return () => clearTimeout(autoSaveTimeout);
  }, [debouncedSaveDocument]); // debouncedSaveDocument

  const createNewDocument = () => {
    setDocumentId(undefined);
    setLastSaved(null);
    if (currentEditor) {
      // Assuming useKiboEditor hook handles resetting the document content
      // or provides a method to do so. If not, direct editor manipulation might be needed:
      // currentEditor.commands.clearContent(true); // or set to default content
      // For now, relying on hook's behavior or subsequent editor update.
    }
  };

  const loadDocument = (document: StoredDocument) => {
    if (!document) {
      return;
    }
    setDocumentId(document.id);
    if (currentEditor) {
      currentEditor.commands.setContent(document.content);
    }
    setLastSaved(new Date(document.updatedAt));
  };

  const containerClass = isFullscreen
    ? 'editor-fullscreen'
    : 'container mx-auto px-4 py-4 mt-24';

  return (
    <div className={containerClass}>
      <EditorHeader editor={currentEditor} />
      <Card className="editor-card animate-editor-in">
        <CardHeader className="editor-header">
          <CardTitle className="flex items-center justify-between">
            <div className="group relative cursor-pointer">
              <div className="editor-title">{doc.title}</div>
              <div className="-bottom-1 absolute left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full" />
            </div>
            <div className="flex gap-2">
              <DocumentSelector
                currentDocId={documentId}
                onCreateNew={createNewDocument}
                onLoadDocument={loadDocument}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="editor-action-button"
              >
                {isFullscreen ? (
                  <Minimize className="h-4 w-4" />
                ) : (
                  <Maximize className="h-4 w-4" />
                )}
                {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="editor-action-button"
                onClick={saveCurrentDocument}
              >
                <Save className="mr-2 h-4 w-4" />
                Save
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="editor-action-button"
                onClick={() => {
                  const timestamp = new Date()
                    .toISOString()
                    .replace(/[:.]/g, '-');
                  const blob = new Blob([doc.content], { type: 'text/html' });
                  const a = document.createElement('a');
                  a.href = URL.createObjectURL(blob);
                  a.download = `${doc.title}-${timestamp}.html`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(a.href);
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                Export HTML
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="editor-action-button"
                  >
                    <Download className="mr-1 h-4 w-4" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                    onClick={exportToDocx}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Export as DOCX
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={exportToPdf}
                    className="flex items-center gap-2"
                  >
                    <File className="h-4 w-4" />
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="editor" className="w-full">
            <TabsList className="editor-tabs w-full">
              <TabsTrigger value="editor" className="editor-tab">
                Document
              </TabsTrigger>
              <TabsTrigger value="preview" className="editor-tab">
                Preview
              </TabsTrigger>
            </TabsList>
            <TabsContent
              value="editor"
              className="prose-lg dark:prose-invert mx-auto max-w-5xl p-4"
            >
              {isMounted && (
                <EditorProvider
                  content={doc.content}
                  onUpdate={handleEditorUpdate}
                  onCreate={({ editor }) => {
                    editorRef.current = editor;
                    setCurrentEditor(editor);
                    if (typeof window !== 'undefined') {
                      window.__KIBO_EDITOR_INSTANCE = editor;
                      setTimeout(() => {
                        editor.commands.focus('end');
                        setEditorReady(true);
                      }, 100); // Reduced delay for faster initialization
                    }
                  }}
                  placeholder="Start writing your thoughts..."
                  className="editor-paper overflow-hidden rounded-lg"
                  extensions={extensions}
                  editable={true}
                  editorProps={{
                    attributes: {
                      class: 'outline-none selection-editor prose-writer',
                      spellcheck: 'true',
                      autocomplete: 'on',
                      autocorrect: 'on',
                      autocapitalize: 'sentences',
                    },
                    handleKeyDown: (_view, event) => {
                      // Enhanced keyboard shortcuts for better writing experience
                      if (event.key === 'Enter' && event.shiftKey) {
                        // Soft break with Shift+Enter
                        return false;
                      }

                      // Quick save with Ctrl+S
                      if (
                        event.key === 's' &&
                        (event.ctrlKey || event.metaKey)
                      ) {
                        event.preventDefault();
                        saveCurrentDocument();
                        // Show save feedback
                        import('sonner').then(({ toast }) => {
                          toast.success('Document saved');
                        });
                        return true;
                      }

                      // Focus mode toggle with Ctrl+Shift+F
                      if (
                        event.key === 'F' &&
                        event.ctrlKey &&
                        event.shiftKey
                      ) {
                        event.preventDefault();
                        toggleFullscreen();
                        return true;
                      }

                      // Export shortcuts
                      if (
                        event.key === 'e' &&
                        event.ctrlKey &&
                        event.shiftKey
                      ) {
                        event.preventDefault();
                        exportToPdf();
                        return true;
                      }

                      if (
                        event.key === 'd' &&
                        event.ctrlKey &&
                        event.shiftKey
                      ) {
                        event.preventDefault();
                        exportToDocx();
                        return true;
                      }

                      // Show keyboard shortcuts with Ctrl+?
                      if (event.key === '?' && event.ctrlKey) {
                        event.preventDefault();
                        setShowKeyboardShortcuts(true);
                        return true;
                      }

                      return false;
                    },
                  }}
                >
                  {currentEditor && (
                    <EditorToolbar
                      editor={currentEditor}
                      onOpenLinkDialog={() => {
                        const url = window.prompt(
                          'Enter link URL:',
                          currentEditor?.getAttributes('link').href
                        );
                        if (url === null) {
                          return;
                        }
                        if (url === '') {
                          currentEditor
                            ?.chain()
                            .focus()
                            .extendMarkRange('link')
                            .unsetLink()
                            .run();
                          return;
                        }
                        currentEditor
                          ?.chain()
                          .focus()
                          .extendMarkRange('link')
                          .setLink({ href: url })
                          .run();
                      }}
                      onTriggerAIPrompt={() => setAiDialogOpen(true)}
                    />
                  )}
                  {currentEditor && editorReady && (
                    <EditorBubbleMenu
                      className="bubble-menu-enhanced"
                      shouldShow={({ state, editor }) => {
                        const { selection } = state;
                        const { empty } = selection;

                        // Only show when there's an actual text selection (not just cursor position)
                        if (empty) {
                          return false;
                        }

                        // Don't show if selection is just whitespace
                        const selectedText = state.doc.textBetween(
                          selection.from,
                          selection.to
                        );
                        if (!selectedText.trim()) {
                          return false;
                        }

                        // Don't show if selection is too small (less than 2 characters)
                        if (selectedText.length < 2) {
                          return false;
                        }

                        // Don't show if editor is not focused
                        if (!editor.isFocused) {
                          return false;
                        }

                        return true;
                      }}
                      tippyOptions={{
                        duration: 200,
                        animation: 'shift-away-subtle',
                        placement: 'top',
                        maxWidth: 'none',
                        offset: [0, 12],
                        delay: [300, 150], // Slightly longer delay to prevent accidental triggers
                        hideOnClick: false,
                        interactive: true,
                        appendTo: () => document.body,
                        theme: 'bubble-menu-theme',
                        arrow: true,
                        zIndex: 9999,
                      }}
                    >
                      <div className="bubble-menu-content">
                        <div className="bubble-menu-section">
                          <EditorFormatBold />
                          <EditorFormatItalic />
                          <EditorFormatUnderline />
                          <EditorFormatStrike />
                          <EditorFormatCode />
                        </div>
                        <div className="bubble-menu-divider" />
                        <div className="bubble-menu-section">
                          <EditorLinkSelector />
                          <Button
                            variant="ghost"
                            size="sm"
                            className="bubble-menu-ai-button"
                            onClick={() => setAiDialogOpen(true)}
                            title="Improve with AI"
                          >
                            ✨
                          </Button>
                        </div>
                        <div className="bubble-menu-divider" />
                        <div className="bubble-menu-section">
                          <EditorNodeHeading1 hideName />
                          <EditorNodeHeading2 hideName />
                          <EditorNodeHeading3 hideName />
                        </div>
                        <div className="bubble-menu-divider" />
                        <div className="bubble-menu-section">
                          <EditorNodeBulletList hideName />
                          <EditorNodeOrderedList hideName />
                          <EditorNodeTaskList hideName />
                          <EditorNodeQuote hideName />
                        </div>
                        <div className="bubble-menu-divider" />
                        <div className="bubble-menu-section">
                          <EditorClearFormatting />
                        </div>
                      </div>
                    </EditorBubbleMenu>
                  )}
                  {currentEditor && editorReady && (
                    <EnhancedFloatingMenu editor={currentEditor} />
                  )}
                  {currentEditor && editorReady && (
                    <EnhancedTableMenu editor={currentEditor} />
                  )}
                  <EditorContent
                    editor={currentEditor}
                    className="prose-lg prose-writer selection-editor enhanced-editor-content min-h-[600px] w-full focus:outline-none"
                  />
                </EditorProvider>
              )}
            </TabsContent>
            <TabsContent
              value="preview"
              className="prose prose-lg dark:prose-invert mx-auto max-w-5xl p-4"
            >
              <SafeHTMLPreview
                content={doc.content}
                className="editor-paper min-h-[500px] rounded-lg p-6"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="editor-footer">
          <div className="flex items-center gap-4">
            <div className="stats-counter">
              <span className="stats-label">Words:</span>
              <span className="stats-value">{wordCount}</span>
            </div>
            <div className="stats-counter">
              <span className="stats-label">Characters:</span>
              <span className="stats-value">{charCount}</span>
            </div>
          </div>
          <div className="text-muted-foreground/80 text-xs">
            {lastSaved
              ? `Last saved: ${lastSaved.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
              : 'Not saved yet'}
          </div>
        </CardFooter>
      </Card>
      <AITextDialog
        open={aiDialogOpen}
        onClose={() => setAiDialogOpen(false)}
        editor={currentEditor}
      />

      {/* Keyboard Shortcuts Dialog */}
      {showKeyboardShortcuts && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="max-w-md rounded-lg bg-background p-6 shadow-xl">
            <h3 className="mb-4 font-semibold text-lg">Keyboard Shortcuts</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Save document</span>
                <kbd className="rounded bg-muted px-2 py-1">Ctrl+S</kbd>
              </div>
              <div className="flex justify-between">
                <span>Toggle fullscreen</span>
                <kbd className="rounded bg-muted px-2 py-1">Ctrl+Shift+F</kbd>
              </div>
              <div className="flex justify-between">
                <span>Export to PDF</span>
                <kbd className="rounded bg-muted px-2 py-1">Ctrl+Shift+E</kbd>
              </div>
              <div className="flex justify-between">
                <span>Export to DOCX</span>
                <kbd className="rounded bg-muted px-2 py-1">Ctrl+Shift+D</kbd>
              </div>
              <div className="flex justify-between">
                <span>Show shortcuts</span>
                <kbd className="rounded bg-muted px-2 py-1">Ctrl+?</kbd>
              </div>
              <div className="flex justify-between">
                <span>Soft line break</span>
                <kbd className="rounded bg-muted px-2 py-1">Shift+Enter</kbd>
              </div>
            </div>
            <button
              type="button"
              onClick={() => setShowKeyboardShortcuts(false)}
              className="mt-4 w-full rounded bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90"
            >
              Close
            </button>
          </div>
        </div>
      )}

      <EditorSplashScreen />
    </div>
  );
}
