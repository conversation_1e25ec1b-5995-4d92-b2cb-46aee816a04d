'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  EditorBubbleMenu,
  EditorClearFormatting,
  EditorFormatBold,
  EditorFormatCode,
  EditorFormatItalic,
  EditorFormatStrike,
  EditorFormatUnderline,
  EditorLinkSelector,
  EditorNodeBulletList,
  EditorNodeCode,
  EditorNodeHeading1,
  EditorNodeHeading2,
  EditorNodeHeading3,
  EditorNodeOrderedList,
  EditorNodeQuote,
  EditorNodeTable,
  EditorProvider,
} from '@/components/ui/kibo-ui/editor';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  exportToDocxEnhanced,
  exportToPdfEnhanced,
} from '@/lib/enhanced-export-utils';
import { setGlobalKiboEditor } from '@/lib/kibo-editor-global';
import { cn } from '@/lib/utils';
import { useMainEditor } from '@/providers/main-editor';
import { type Editor, EditorContent } from '@tiptap/react';
import {
  Code,
  Download,
  FileText,
  Maximize2,
  Minimize2,
  Redo,
  Undo,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import '../../app/word-editor/editor-bubble-compact.css';
import '../../app/word-editor/editor-bubble-premium.css';
import '../../app/word-editor/editor-ultra-modern.css';
import '../../app/word-editor/editor-advanced-features.css';
import './canvas-editor-enhanced.css';
import { EnhancedFloatingMenu } from '@/components/word-editor/enhanced-floating-menu';
import { EditorHeader } from '@/components/word-editor/header';

const wordRegex = /\s+/;

export const EnhancedMainEditorPanel = () => {
  const { content, setContent } = useMainEditor();
  const editorRef = useRef<Editor | null>(null);
  const [currentEditorInstance, setCurrentEditorInstance] =
    useState<Editor | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [focusMode, setFocusMode] = useState(false);
  const [highContrast, setHighContrast] = useState(false);
  const [typewriterMode, setTypewriterMode] = useState(false);
  const [distractionFree, setDistractionFree] = useState(false);

  const handleEditorUpdate = ({ editor }: { editor: Editor }) => {
    const html = editor.getHTML();
    setContent(html);
    if (editor.storage.characterCount) {
      setCharCount(editor.storage.characterCount.characters());
    }
    const text = editor.getText();
    setWordCount(text ? text.trim().split(wordRegex).length : 0);
  };

  useEffect(() => {
    if (currentEditorInstance) {
      setGlobalKiboEditor(currentEditorInstance as any); // Consider a more type-safe approach if possible
      return () => {
        setGlobalKiboEditor(null);
      };
    }
  }, [currentEditorInstance]);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (typeof document !== 'undefined' && document.body) {
      if (isFullscreen) {
        document.body.classList.remove('overflow-hidden');
      } else {
        document.body.classList.add('overflow-hidden');
      }
    }
  };

  const handleUndo = () => {
    currentEditorInstance?.chain().focus().undo().run();
  };

  const handleRedo = () => {
    currentEditorInstance?.chain().focus().redo().run();
  };

  return (
    <div
      className={cn(
        'flex h-full flex-col',
        isFullscreen ? 'fixed inset-0 z-50 bg-background p-4' : '',
        focusMode && 'editor-focus-mode',
        highContrast && 'high-contrast-mode',
        typewriterMode && 'typewriter-mode',
        distractionFree && 'distraction-free-mode',
        'enhanced-selection enhanced-cursor'
      )}
    >
      <EditorHeader editor={currentEditorInstance} />
      <div className="mb-3 flex items-center justify-end border-border/60 border-b bg-muted/20 px-4 py-2 backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleUndo}
                  disabled={!currentEditorInstance?.can().undo()}
                  aria-label="Undo"
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleRedo}
                  disabled={!currentEditorInstance?.can().redo()}
                  aria-label="Redo"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Redo</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="mx-1 h-6" />

          {/* Preview button removed for better writing experience */}

          <DropdownMenu>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1"
                      aria-label="Export content"
                    >
                      <Download className="mr-1 h-4 w-4" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>Export content</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => {
                  const blob = new Blob([content || ''], { type: 'text/html' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `content-${new Date().toISOString().slice(0, 10)}.html`;
                  if (typeof document !== 'undefined') {
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }
                  URL.revokeObjectURL(url);
                }}
              >
                <Code className="h-4 w-4" />
                Export as HTML
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => {
                  const text = currentEditorInstance?.getText() || '';
                  const blob = new Blob([text], { type: 'text/plain' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `content-${new Date().toISOString().slice(0, 10)}.txt`;
                  if (typeof document !== 'undefined') {
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }
                  URL.revokeObjectURL(url);
                }}
              >
                <FileText className="h-4 w-4" />
                Export as Text
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={async () => {
                  try {
                    await exportToPdfEnhanced({
                      title: 'Tersa Document',
                      content: content || '',
                      createdAt: new Date(),
                    });
                  } catch (error) {
                    console.error('PDF export failed:', error);
                  }
                }}
              >
                <FileText className="h-4 w-4" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={async () => {
                  try {
                    await exportToDocxEnhanced({
                      title: 'Tersa Document',
                      content: content || '',
                      createdAt: new Date(),
                    });
                  } catch (error) {
                    console.error('DOCX export failed:', error);
                  }
                }}
              >
                <FileText className="h-4 w-4" />
                Export as DOCX
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1"
                      aria-label="View options"
                    >
                      <span className="mr-1 text-sm">👁️</span>
                      View
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>View options</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => setFocusMode(!focusMode)}
              >
                <span className="text-sm">🎯</span>
                {focusMode ? 'Exit Focus Mode' : 'Focus Mode'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => setHighContrast(!highContrast)}
              >
                <span className="text-sm">👁️</span>
                {highContrast ? 'Normal Contrast' : 'High Contrast'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => setTypewriterMode(!typewriterMode)}
              >
                <span className="text-sm">⌨️</span>
                {typewriterMode ? 'Exit Typewriter' : 'Typewriter Mode'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => setDistractionFree(!distractionFree)}
              >
                <span className="text-sm">🧘</span>
                {distractionFree ? 'Show UI' : 'Distraction Free'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleFullscreen}
                  className="ml-1"
                  aria-label={
                    isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'
                  }
                >
                  {isFullscreen ? (
                    <Minimize2 className="h-4 w-4" />
                  ) : (
                    <Maximize2 className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      <div className="flex flex-grow flex-col">
        <EditorProvider
          content={content}
          onUpdate={handleEditorUpdate}
          onCreate={({ editor }: { editor: Editor }) => {
            editorRef.current = editor;
            setCurrentEditorInstance(editor);
            // Set global editor instance for toolbar buttons
            if (typeof window !== 'undefined') {
              window.__KIBO_EDITOR_INSTANCE = editor;
            }
          }}
          placeholder="Start writing your thoughts..."
          className={cn(
            'canvas-editor-enhanced flex-grow overflow-hidden rounded-md border bg-card',
            'shadow-sm transition-all duration-200'
          )}
          editorProps={{
            attributes: {
              class: 'outline-none selection-editor prose-writer-canvas',
              spellcheck: 'true',
              autocomplete: 'on',
              autocorrect: 'on',
              autocapitalize: 'sentences',
            },
          }}
        >
          {currentEditorInstance && (
            <>
              <EditorBubbleMenu
                className="bubble-menu-enhanced"
                shouldShow={({ state, editor }) => {
                  const { selection } = state;
                  const { empty } = selection;

                  // Only show when there's actual text selection (not just cursor position)
                  if (empty) {
                    return false;
                  }

                  // Don't show if selection is just whitespace
                  const selectedText = state.doc.textBetween(
                    selection.from,
                    selection.to
                  );
                  if (!selectedText.trim()) {
                    return false;
                  }

                  // Don't show if selection is too small (less than 2 characters)
                  if (selectedText.length < 2) {
                    return false;
                  }

                  // Don't show if editor is not focused
                  if (!editor.isFocused) {
                    return false;
                  }

                  return true;
                }}
                tippyOptions={{
                  duration: 200,
                  animation: 'shift-away-subtle',
                  placement: 'top',
                  maxWidth: 'none',
                  offset: [0, 12],
                  delay: [400, 150], // Longer delay to prevent accidental triggers
                  hideOnClick: false,
                  interactive: true,
                  appendTo: () => document.body,
                  theme: 'bubble-menu-theme',
                  arrow: true,
                  zIndex: 9999,
                }}
              >
                <div className="bubble-menu-content">
                  <div className="bubble-menu-section">
                    <EditorFormatBold />
                    <EditorFormatItalic />
                    <EditorFormatUnderline />
                    <EditorFormatStrike />
                    <EditorFormatCode />
                  </div>
                  <div className="bubble-menu-divider" />
                  <div className="bubble-menu-section">
                    <EditorLinkSelector />
                  </div>
                  <div className="bubble-menu-divider" />
                  <div className="bubble-menu-section">
                    <EditorNodeHeading1 />
                    <EditorNodeHeading2 />
                    <EditorNodeHeading3 />
                  </div>
                  <div className="bubble-menu-divider" />
                  <div className="bubble-menu-section">
                    <EditorNodeBulletList />
                    <EditorNodeOrderedList />
                    <EditorNodeQuote />
                  </div>
                  <div className="bubble-menu-divider" />
                  <div className="bubble-menu-section">
                    <EditorNodeCode />
                    <EditorNodeTable />
                  </div>
                  <div className="bubble-menu-divider" />
                  <div className="bubble-menu-section">
                    <EditorClearFormatting />
                  </div>
                </div>
              </EditorBubbleMenu>

              <EnhancedFloatingMenu editor={currentEditorInstance} />
            </>
          )}
          <EditorContent editor={currentEditorInstance} />
        </EditorProvider>

        <div className="mt-3 flex justify-between rounded-md bg-muted/30 px-3 py-2 font-medium text-muted-foreground text-xs backdrop-blur-sm">
          <div className="flex items-center gap-1">
            <span className="text-primary">●</span>
            Words: {wordCount}
          </div>
          <div className="flex items-center gap-1">
            <span className="text-primary">●</span>
            Characters: {charCount}
          </div>
        </div>
      </div>
    </div>
  );
};
