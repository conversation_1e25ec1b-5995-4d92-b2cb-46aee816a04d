import { NodeLayout } from '@/components/nodes/layout';
import {
  Dropzone,
  DropzoneContent,
  DropzoneEmptyState,
} from '@/components/ui/kibo-ui/dropzone';
import { handleError } from '@/lib/error/handle';
import { uploadFile } from '@/lib/upload';
import { useReactFlow } from '@xyflow/react';
import { FileIcon, Loader2Icon } from 'lucide-react';
import { useState } from 'react';
import type { FileNodeProps } from '.';

type FilePrimitiveProps = FileNodeProps & {
  title: string;
};

const FilePreview = ({
  name,
  type,
  url,
}: { name: string; type: string; url: string }) => (
  <div className="flex flex-col gap-2">
    <div className="flex items-center gap-2">
      <FileIcon size={16} className="text-muted-foreground" />
      <span className="truncate font-medium text-sm">{name}</span>
    </div>
    {type === 'application/pdf' ? (
      <div className="relative aspect-[3/4] w-full overflow-hidden rounded border">
        <iframe
          src={`${url}#view=FitH`}
          className="absolute inset-0 h-full w-full"
          title="PDF Preview"
        />
      </div>
    ) : (
      <div className="flex items-center justify-center rounded border p-4">
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center gap-2 text-primary text-sm hover:underline"
        >
          <FileIcon size={16} />
          <span>Download File</span>
        </a>
      </div>
    )}
  </div>
);

export const FilePrimitive = ({
  data,
  id,
  type,
  title,
}: FilePrimitiveProps) => {
  const { updateNodeData } = useReactFlow();
  const [files, setFiles] = useState<File[] | undefined>();
  const [isUploading, setIsUploading] = useState(false);

  const validateFile = (file: File) => {
    // Validate file size
    if (file.size < 1024) {
      throw new Error('File is too small. Minimum size is 1KB.');
    }

    if (file.size > 1024 * 1024 * 10) {
      throw new Error('File is too large. Maximum size is 10MB.');
    }

    // Check for specific file types that should use other nodes
    if (file.type.startsWith('audio/')) {
      throw new Error(
        'Audio files should be uploaded using the Audio node. Please use the Audio node instead.'
      );
    }

    if (file.type.startsWith('video/')) {
      throw new Error(
        'Video files should be uploaded using the Video node. Please use the Video node instead.'
      );
    }

    if (file.type.startsWith('image/')) {
      throw new Error(
        'Image files should be uploaded using the Image node. Please use the Image node instead.'
      );
    }
  };

  const handleDrop = async (files: File[]) => {
    if (isUploading) {
      return;
    }

    try {
      if (!files.length) {
        throw new Error('No file selected');
      }

      const file = files[0];
      validateFile(file);

      setIsUploading(true);
      setFiles(files);

      const { url, type } = await uploadFile(file, 'files');

      updateNodeData(id, {
        content: {
          url,
          name: file.name,
          type,
        },
      });
    } catch (error) {
      handleError('File upload failed', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <NodeLayout id={id} data={data} type={type} title={title}>
      <div className="p-4">
        {data.content ? (
          <FilePreview {...data.content} />
        ) : (
          <Dropzone
            maxSize={1024 * 1024 * 10}
            minSize={1024}
            maxFiles={1}
            multiple={false}
            accept={{
              'application/pdf': ['.pdf'],
              'application/msword': ['.doc'],
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                ['.docx'],
              'text/plain': ['.txt'],
              'text/csv': ['.csv'],
              'application/vnd.ms-excel': ['.xls'],
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                ['.xlsx'],
              'application/zip': ['.zip'],
              'application/x-rar-compressed': ['.rar'],
              'application/json': ['.json'],
              'application/xml': ['.xml'],
              'text/html': ['.html'],
            }}
            onDrop={handleDrop}
            src={files}
            onError={(error) => handleError('File upload error', error)}
            className="rounded-none border-none bg-transparent shadow-none hover:bg-transparent dark:bg-transparent dark:hover:bg-transparent"
          >
            <DropzoneEmptyState />
            <DropzoneContent>
              {files && files.length > 0 && (
                <div className="relative">
                  <FilePreview
                    name={files[0].name}
                    type={files[0].type}
                    url={URL.createObjectURL(files[0])}
                  />
                  <div className="absolute inset-0 z-10 flex items-center justify-center rounded-lg bg-black/50">
                    <Loader2Icon className="size-12 animate-spin text-white" />
                  </div>
                </div>
              )}
            </DropzoneContent>
          </Dropzone>
        )}
      </div>
    </NodeLayout>
  );
};
