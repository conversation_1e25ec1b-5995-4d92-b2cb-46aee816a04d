import { NodeLayout } from '@/components/nodes/layout';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { useAnalytics } from '@/hooks/use-analytics';
import { handleError } from '@/lib/error/handle';
import { textModels } from '@/lib/models/text';
import {
  getDescriptionsFromImageNodes,
  getFilesFromFileNodes,
  getImagesFromImageNodes,
  getTextFromTextNodes,
  getTranscriptionFromAudioNodes,
} from '@/lib/xyflow';
import { addContentToMainEditor } from '@/providers/main-editor';
import { useChat } from '@ai-sdk/react';
import { getIncomers, useReactFlow } from '@xyflow/react';
import {
  ClockIcon,
  ExternalLinkIcon,
  PlayIcon,
  RotateCcwIcon,
  SquareIcon,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import type { ChangeEventHandler, ComponentProps } from 'react';
import ReactMarkdown from 'react-markdown';
import { toast } from 'sonner';
import { mutate } from 'swr';
import type { TextNodeProps } from '.';
import { ModelSelector } from '../model-selector';

type TextTransformProps = TextNodeProps & {
  title: string;
};

// Helper function to format content for word editor
const formatContentForWordEditor = (content: string): string => {
  if (!content) {
    return '';
  }

  // If content is already HTML, return as is
  if (content.includes('<') && content.includes('>')) {
    return content;
  }

  // Convert plain text to properly formatted HTML
  return content
    .split('\n\n')
    .map((paragraph) => paragraph.trim())
    .filter((paragraph) => paragraph.length > 0)
    .map((paragraph) => {
      // Handle headings
      if (paragraph.startsWith('# ')) {
        return `<h1>${paragraph.substring(2)}</h1>`;
      }
      if (paragraph.startsWith('## ')) {
        return `<h2>${paragraph.substring(3)}</h2>`;
      }
      if (paragraph.startsWith('### ')) {
        return `<h3>${paragraph.substring(4)}</h3>`;
      }

      // Handle bold text
      let formatted = paragraph.replace(
        /\*\*(.*?)\*\*/g,
        '<strong>$1</strong>'
      );

      // Handle italic text
      formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // Handle line breaks within paragraphs
      formatted = formatted.replace(/\n/g, '<br>');

      return `<p>${formatted}</p>`;
    })
    .join('');
};

const getDefaultModel = (models: typeof textModels) => {
  const defaultModel = models
    .flatMap((model) => model.models)
    .find((model) => model.default);

  if (!defaultModel) {
    throw new Error('No default model found');
  }

  return defaultModel;
};

export const TextTransform = ({
  data,
  id,
  type,
  title,
}: TextTransformProps) => {
  const { updateNodeData, getNodes, getEdges } = useReactFlow();
  const { projectId } = useParams();
  const modelId = data.model ?? getDefaultModel(textModels).id;
  const analytics = useAnalytics();
  const { append, messages, setMessages, status, stop } = useChat({
    body: {
      modelId,
    },
    onError: (error) => handleError('Error generating text', error),
    onFinish: (message) => {
      updateNodeData(id, {
        generated: {
          text: message.content,
        },
        updatedAt: new Date().toISOString(),
      });

      toast.success('Text generated successfully');

      setTimeout(() => mutate('credits'), 5000);
    },
  });

  const handleGenerate = async () => {
    const incomers = getIncomers({ id }, getNodes(), getEdges());
    const textPrompts = getTextFromTextNodes(incomers);
    const audioPrompts = getTranscriptionFromAudioNodes(incomers);
    const images = getImagesFromImageNodes(incomers);
    const imageDescriptions = getDescriptionsFromImageNodes(incomers);
    const files = getFilesFromFileNodes(incomers);

    if (!textPrompts.length && !audioPrompts.length && !data.instructions) {
      handleError('Error generating text', 'No prompts found');
      return;
    }

    const content: string[] = [];

    if (data.instructions) {
      content.push('--- Instructions ---', data.instructions);
    }

    if (textPrompts.length) {
      content.push('--- Text Prompts ---', ...textPrompts);
    }

    if (audioPrompts.length) {
      content.push('--- Audio Prompts ---', ...audioPrompts);
    }

    if (imageDescriptions.length) {
      content.push('--- Image Descriptions ---', ...imageDescriptions);
    }

    analytics.track('canvas', 'node', 'generate', {
      type,
      promptLength: content.join('\n').length,
      model: modelId,
      instructionsLength: data.instructions?.length ?? 0,
      imageCount: images.length,
      fileCount: files.length,
    });

    setMessages([]);
    append({
      role: 'user',
      content: content.join('\n'),
      experimental_attachments: [
        ...images.map((image) => ({
          url: image.url,
          contentType: image.type,
        })),
        ...files.map((file) => ({
          url: file.url,
          contentType: file.type,
          name: file.name,
        })),
      ],
    });
  };

  const handleInstructionsChange: ChangeEventHandler<HTMLTextAreaElement> = (
    event
  ) => updateNodeData(id, { instructions: event.target.value });

  const createToolbar = (): ComponentProps<typeof NodeLayout>['toolbar'] => {
    const toolbar: ComponentProps<typeof NodeLayout>['toolbar'] = [];

    toolbar.push({
      children: (
        <ModelSelector
          value={modelId}
          options={textModels}
          key={id}
          className="w-[200px] rounded-full"
          onChange={(value) => updateNodeData(id, { model: value })}
        />
      ),
    });

    if (status === 'submitted') {
      toolbar.push({
        tooltip: 'Stop',
        children: (
          <Button
            size="icon"
            className="rounded-full"
            onClick={stop}
            disabled={!projectId}
          >
            <SquareIcon size={12} />
          </Button>
        ),
      });
    } else if (messages.length || data.generated?.text) {
      toolbar.push({
        tooltip: 'Regenerate',
        children: (
          <Button
            size="icon"
            className="rounded-full"
            onClick={handleGenerate}
            disabled={!projectId}
          >
            <RotateCcwIcon size={12} />
          </Button>
        ),
      });
    } else {
      toolbar.push({
        tooltip: 'Generate',
        children: (
          <Button
            size="icon"
            className="rounded-full"
            onClick={handleGenerate}
            disabled={!projectId}
          >
            <PlayIcon size={12} />
          </Button>
        ),
      });
    }

    if (data.updatedAt) {
      toolbar.push({
        tooltip: `Last updated: ${new Intl.DateTimeFormat('en-US', {
          dateStyle: 'short',
          timeStyle: 'short',
        }).format(new Date(data.updatedAt))}`,
        children: (
          <Button size="icon" variant="ghost" className="rounded-full">
            <ClockIcon size={12} />
          </Button>
        ),
      });
    }

    // Add "Send to Main Editor" button when content is available
    if (
      (data.generated?.text || nonUserMessages.length > 0) &&
      status !== 'submitted'
    ) {
      toolbar.push({
        tooltip: 'Send to Main Editor Panel',
        children: (
          <Button
            size="icon"
            className="rounded-full"
            onClick={() => {
              const content =
                data.generated?.text || nonUserMessages.at(-1)?.content;
              if (content) {
                addContentToMainEditor(content);
                toast.success('Content sent to Main Editor Panel');
                analytics.track('editor', 'content', 'sent-main');
              }
            }}
          >
            <ExternalLinkIcon size={12} />
          </Button>
        ),
      });

      // Add "Send to Word Editor" button
      toolbar.push({
        tooltip: 'Send to Word Editor',
        children: (
          <Button
            size="icon"
            className="rounded-full"
            variant="outline"
            onClick={() => {
              const content =
                data.generated?.text || nonUserMessages.at(-1)?.content;
              if (content) {
                // Store content for word editor
                try {
                  // Format content properly for the word editor
                  const formattedContent = formatContentForWordEditor(content);

                  const pendingContent = {
                    id:
                      Date.now().toString(36) +
                      Math.random().toString(36).substring(2),
                    content: formattedContent,
                    type: 'html' as const,
                    timestamp: Date.now(),
                    source: 'text-node',
                  };
                  localStorage.setItem(
                    'tersa_pending_word_editor_content',
                    JSON.stringify(pendingContent)
                  );

                  // Try to add directly if word editor is open
                  if (
                    typeof window !== 'undefined' &&
                    window.__KIBO_EDITOR_INSTANCE
                  ) {
                    const editor = window.__KIBO_EDITOR_INSTANCE;
                    editor.commands.focus('end');
                    const currentContent = editor.getHTML();
                    if (currentContent && !currentContent.endsWith('<p></p>')) {
                      editor.commands.insertContent('\n\n');
                    }
                    editor.commands.insertContent(content);
                    localStorage.removeItem(
                      'tersa_pending_word_editor_content'
                    );
                    toast.success('Content sent to Word Editor');
                  } else {
                    toast.success(
                      'Content saved for Word Editor - open Word Editor to see it'
                    );
                  }

                  analytics.track('editor', 'content', 'sent-word');
                } catch (error) {
                  toast.error('Failed to send content to Word Editor');
                }
              }
            }}
          >
            <svg
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              aria-label="Send to Word Editor"
            >
              <title>Send to Word Editor</title>
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14,2 14,8 20,8" />
              <line x1="16" y1="13" x2="8" y2="13" />
              <line x1="16" y1="17" x2="8" y2="17" />
              <polyline points="10,9 9,9 8,9" />
            </svg>
          </Button>
        ),
      });
    }

    return toolbar;
  };

  const nonUserMessages = messages.filter((message) => message.role !== 'user');

  return (
    <NodeLayout
      id={id}
      data={data}
      title={title}
      type={type}
      toolbar={createToolbar()}
    >
      <div className="nowheel h-full max-h-[30rem] flex-1 overflow-auto rounded-t-3xl rounded-b-xl bg-secondary p-4">
        {status === 'submitted' && (
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-60 animate-pulse rounded-lg" />
            <Skeleton className="h-4 w-40 animate-pulse rounded-lg" />
            <Skeleton className="h-4 w-50 animate-pulse rounded-lg" />
          </div>
        )}
        {data.generated?.text &&
          !nonUserMessages.length &&
          status !== 'submitted' && (
            <ReactMarkdown>{data.generated.text}</ReactMarkdown>
          )}
        {!data.generated?.text &&
          !nonUserMessages.length &&
          status !== 'submitted' && (
            <div className="flex aspect-video w-full items-center justify-center bg-secondary">
              <p className="text-muted-foreground text-sm">
                Press <PlayIcon size={12} className="-translate-y-px inline" />{' '}
                to generate text
              </p>
            </div>
          )}
        {Boolean(nonUserMessages.length) &&
          status !== 'submitted' &&
          nonUserMessages.map((message, index) => (
            <ReactMarkdown key={index}>{message.content}</ReactMarkdown>
          ))}
      </div>
      <Textarea
        value={data.instructions ?? ''}
        onChange={handleInstructionsChange}
        placeholder="Enter instructions"
        className="shrink-0 resize-none rounded-none border-none bg-transparent! shadow-none focus-visible:ring-0"
      />
    </NodeLayout>
  );
};
