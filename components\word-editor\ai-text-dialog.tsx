'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import type { Editor } from '@tiptap/core';
import { FileText, MessageSquare, Sparkles, Wand2 } from 'lucide-react';
import { useState } from 'react';

interface AITextDialogProps {
  open: boolean;
  onClose: () => void;
  editor: Editor | null;
  initialMode?: AIAction;
}

type AIAction = 'generate' | 'improve' | 'summarize' | 'chat';

export function AITextDialog({ open, onClose, editor }: AITextDialogProps) {
  const [activeTab, setActiveTab] = useState<AIAction>('generate');
  const [prompt, setPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [aiResult, setAIResult] = useState('');

  // Real AI processing function using the chat API
  const processWithAI = async () => {
    if (!prompt.trim() || !editor) {
      console.log('❌ AI Processing: Missing prompt or editor', {
        prompt: prompt.trim(),
        editor: !!editor,
      });
      return;
    }

    console.log('🚀 Starting AI processing...', { activeTab, prompt });
    setIsProcessing(true);

    try {
      // Get selected text for context
      const selectedText = editor.state.selection.empty
        ? ''
        : editor.state.doc.textBetween(
            editor.state.selection.from,
            editor.state.selection.to
          );

      console.log('📝 Text context:', {
        selectedText,
        hasSelection: !editor.state.selection.empty,
      });

      // Build system message based on action type
      let systemMessage = '';
      let userMessage = prompt;

      switch (activeTab) {
        case 'generate':
          systemMessage =
            "You are a helpful writing assistant. Generate high-quality content based on the user's prompt. Be creative and engaging.";
          break;
        case 'improve':
          systemMessage =
            'You are a professional editor. Improve the given text by enhancing clarity, flow, and engagement while maintaining the original meaning.';
          if (selectedText) {
            userMessage = `Please improve this text: "${selectedText}"\n\nInstructions: ${prompt}`;
          }
          break;
        case 'summarize':
          systemMessage =
            'You are a skilled summarizer. Create concise, informative summaries that capture the key points.';
          const documentText = editor.getText();
          if (documentText) {
            userMessage = `Please summarize this document: "${documentText}"\n\nPreferences: ${prompt}`;
          }
          break;
        case 'chat':
          systemMessage =
            "You are a helpful writing assistant. Answer questions and provide writing guidance based on the user's document context.";
          const contextText = editor.getText();
          if (contextText) {
            userMessage = `Document context: "${contextText}"\n\nQuestion: ${prompt}`;
          }
          break;
      }

      const requestPayload = {
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage },
        ],
        modelId: 'openai-gpt-4o', // Use default model
      };

      console.log('📤 Making API request to /api/chat:', requestPayload);

      // Make API call to chat endpoint
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestPayload),
      });

      console.log('📥 API Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        throw new Error(
          `API request failed: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        console.error('❌ No response body reader available');
        throw new Error('No response body');
      }

      console.log('🔄 Starting to read streaming response...');
      let result = '';
      let chunkCount = 0;
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('✅ Streaming complete. Total chunks:', chunkCount);
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);
        console.log(`📦 Chunk ${chunkCount}:`, chunk);

        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('0:')) {
            try {
              const data = JSON.parse(line.slice(2));
              console.log('📊 Parsed data:', data);
              if (data.type === 'text-delta') {
                result += data.textDelta;
                setAIResult(result); // Update UI in real-time
                console.log('✏️ Updated result length:', result.length);
              }
            } catch (e) {
              console.warn('⚠️ Failed to parse chunk line:', line, e);
            }
          }
        }
      }

      console.log('🎉 Final AI result:', {
        length: result.length,
        preview: result.substring(0, 100),
      });
      setAIResult(result);
    } catch (error) {
      console.error('❌ AI processing error:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to process with AI. Please try again.';
      console.error('❌ Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: errorMessage,
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });
      setAIResult(`Error: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
      console.log('🏁 AI processing finished');
    }
  };

  const insertContentToEditor = () => {
    if (!editor || !aiResult) return;

    editor.chain().focus().insertContent(aiResult).run();
    resetAndClose();
  };

  const resetAndClose = () => {
    setPrompt('');
    setAIResult('');
    setActiveTab('generate');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            AI Text Assistant
          </DialogTitle>
          <DialogDescription>
            Use AI to enhance your writing with different tools
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as AIAction)}
          className="w-full"
        >
          <TabsList className="mb-4 grid grid-cols-4">
            <TabsTrigger value="generate" className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              <span className="hidden sm:inline">Generate</span>
            </TabsTrigger>
            <TabsTrigger value="improve" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span className="hidden sm:inline">Improve</span>
            </TabsTrigger>
            <TabsTrigger value="summarize" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Summarize</span>
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Chat</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate">
            <p className="mb-2 text-muted-foreground text-sm">
              Generate new content based on your prompt
            </p>
          </TabsContent>

          <TabsContent value="improve">
            <p className="mb-2 text-muted-foreground text-sm">
              Enhance selected text with better wording and structure
            </p>
          </TabsContent>

          <TabsContent value="summarize">
            <p className="mb-2 text-muted-foreground text-sm">
              Create a concise summary of your document or selection
            </p>
          </TabsContent>

          <TabsContent value="chat">
            <p className="mb-2 text-muted-foreground text-sm">
              Ask questions about your document or get writing assistance
            </p>
          </TabsContent>
        </Tabs>

        <div className="mt-2 flex flex-col gap-4">
          <Textarea
            placeholder={`Enter your ${
              activeTab === 'generate'
                ? 'prompt for new content'
                : activeTab === 'improve'
                  ? 'instructions for improvement'
                  : activeTab === 'summarize'
                    ? 'summarization preferences'
                    : 'question or request'
            }`}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={3}
            className="resize-none"
            disabled={isProcessing}
          />

          {aiResult && (
            <div className="max-h-[200px] overflow-y-auto rounded-md border bg-muted/30 p-3">
              <pre className="whitespace-pre-wrap text-sm">{aiResult}</pre>
            </div>
          )}
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button variant="outline" onClick={resetAndClose}>
            Cancel
          </Button>

          <div className="flex gap-2">
            {aiResult ? (
              <Button
                onClick={insertContentToEditor}
                className="flex items-center gap-1"
              >
                <FileText className="h-4 w-4" />
                Insert to Document
              </Button>
            ) : (
              <Button
                onClick={processWithAI}
                disabled={!prompt.trim() || isProcessing}
                className="flex items-center gap-1"
              >
                {isProcessing ? (
                  <>
                    <span className="mr-1 animate-spin">⏳</span>
                    Processing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Process with AI
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
