'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Toggle } from '@/components/ui/toggle';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { Editor } from '@tiptap/core';
import {
  BoldIcon,
  ChevronDownIcon,
  CodeIcon,
  Heading1Icon,
  Heading2Icon,
  Heading3Icon,
  ImageIcon,
  ItalicIcon,
  LinkIcon,
  ListIcon,
  ListOrderedIcon,
  ListTodoIcon,
  MinusIcon,
  PilcrowIcon,
  QuoteIcon,
  SparklesIcon,
  StrikethroughIcon,
  TableIcon,
  TerminalSquareIcon,
  UnderlineIcon,
} from 'lucide-react';
import type React from 'react';

export interface EditorToolbarProps {
  editor: Editor | null;
  aiDisabled?: boolean;
  onTriggerAIPrompt?: () => void;
  onOpenLinkDialog?: () => void;
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({
  editor,
  aiDisabled = false,
  onTriggerAIPrompt,
  onOpenLinkDialog,
}) => {
  if (!editor) {
    return null;
  }

  const currentTextStyleLabel = () => {
    if (editor.isActive('heading', { level: 1 })) return 'Heading 1';
    if (editor.isActive('heading', { level: 2 })) return 'Heading 2';
    if (editor.isActive('heading', { level: 3 })) return 'Heading 3';
    return 'Paragraph';
  };

  return (
    <TooltipProvider delayDuration={150}>
      <div className="sticky top-0 z-10 flex flex-wrap items-center gap-0.5 border-b bg-background p-1.5 print:hidden">
        {/* Text Style Dropdown */}
        <DropdownMenu>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-32 justify-start px-2 text-xs"
                >
                  <span>{currentTextStyleLabel()}</span>
                  <ChevronDownIcon className="ml-auto h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Text Style</p>
            </TooltipContent>
          </Tooltip>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setParagraph().run()}
              className="flex items-center gap-2 text-xs data-[state=checked]:bg-accent"
              data-state={
                editor.isActive('paragraph') ? 'checked' : 'unchecked'
              }
            >
              <PilcrowIcon className="h-4 w-4" /> Paragraph
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 1 }).run()
              }
              className="flex items-center gap-2 text-sm data-[state=checked]:bg-accent"
              data-state={
                editor.isActive('heading', { level: 1 })
                  ? 'checked'
                  : 'unchecked'
              }
            >
              <Heading1Icon className="h-4 w-4" /> Heading 1
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 2 }).run()
              }
              className="flex items-center gap-2 text-xs data-[state=checked]:bg-accent"
              data-state={
                editor.isActive('heading', { level: 2 })
                  ? 'checked'
                  : 'unchecked'
              }
            >
              <Heading2Icon className="h-4 w-4" /> Heading 2
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 3 }).run()
              }
              className="flex items-center gap-2 text-xs data-[state=checked]:bg-accent"
              data-state={
                editor.isActive('heading', { level: 3 })
                  ? 'checked'
                  : 'unchecked'
              }
            >
              <Heading3Icon className="h-4 w-4" /> Heading 3
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="mx-1.5 h-5 w-px bg-border"></div>

        {/* Bold */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('bold')}
              onPressedChange={() => editor.chain().focus().toggleBold().run()}
              aria-label="Toggle bold"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <BoldIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bold (Ctrl+B)</p>
          </TooltipContent>
        </Tooltip>

        {/* Italic */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('italic')}
              onPressedChange={() =>
                editor.chain().focus().toggleItalic().run()
              }
              aria-label="Toggle italic"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <ItalicIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Italic (Ctrl+I)</p>
          </TooltipContent>
        </Tooltip>

        {/* Underline */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('underline')}
              onPressedChange={() =>
                editor.chain().focus().toggleUnderline().run()
              }
              aria-label="Toggle underline"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <UnderlineIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Underline (Ctrl+U)</p>
          </TooltipContent>
        </Tooltip>

        {/* Strikethrough */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('strike')}
              onPressedChange={() =>
                editor.chain().focus().toggleStrike().run()
              }
              aria-label="Toggle strikethrough"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <StrikethroughIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Strikethrough (Ctrl+Shift+X)</p>
          </TooltipContent>
        </Tooltip>

        {/* Link Button */}
        {onOpenLinkDialog && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Toggle
                size="sm"
                pressed={editor.isActive('link')}
                onPressedChange={onOpenLinkDialog}
                aria-label="Edit link"
                className="h-8 w-8 p-0 data-[state=on]:bg-muted"
              >
                <LinkIcon className="h-4 w-4" />
              </Toggle>
            </TooltipTrigger>
            <TooltipContent>
              <p>Link (Ctrl+K)</p>
            </TooltipContent>
          </Tooltip>
        )}

        <div className="mx-1.5 h-5 w-px bg-border"></div>

        {/* Bullet List */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('bulletList')}
              onPressedChange={() =>
                editor.chain().focus().toggleBulletList().run()
              }
              aria-label="Toggle bullet list"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <ListIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Bullet List</p>
          </TooltipContent>
        </Tooltip>

        {/* Ordered List */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('orderedList')}
              onPressedChange={() =>
                editor.chain().focus().toggleOrderedList().run()
              }
              aria-label="Toggle ordered list"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <ListOrderedIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Ordered List</p>
          </TooltipContent>
        </Tooltip>

        {/* Task List */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('taskList')}
              onPressedChange={() =>
                editor.chain().focus().toggleTaskList().run()
              }
              aria-label="Toggle task list"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <ListTodoIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Task List</p>
          </TooltipContent>
        </Tooltip>

        <div className="mx-1.5 h-5 w-px bg-border"></div>

        {/* Blockquote */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('blockquote')}
              onPressedChange={() =>
                editor.chain().focus().toggleBlockquote().run()
              }
              aria-label="Toggle blockquote"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <QuoteIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Blockquote</p>
          </TooltipContent>
        </Tooltip>

        {/* Code Block */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('codeBlock')}
              onPressedChange={() =>
                editor.chain().focus().toggleCodeBlock().run()
              }
              aria-label="Toggle code block"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <TerminalSquareIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Code Block</p>
          </TooltipContent>
        </Tooltip>

        {/* Code */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Toggle
              size="sm"
              pressed={editor.isActive('code')}
              onPressedChange={() => editor.chain().focus().toggleCode().run()}
              aria-label="Toggle inline code"
              className="h-8 w-8 p-0 data-[state=on]:bg-muted"
            >
              <CodeIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>
            <p>Inline Code</p>
          </TooltipContent>
        </Tooltip>

        {/* Table */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={() =>
                editor
                  .chain()
                  .focus()
                  .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                  .run()
              }
              className="h-8 w-8 p-0"
            >
              <TableIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Insert Table</p>
          </TooltipContent>
        </Tooltip>

        {/* Image */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                const url = window.prompt('Enter image URL');
                if (url) {
                  editor.chain().focus().setImage({ src: url }).run();
                }
              }}
              className="h-8 w-8 p-0"
            >
              <ImageIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Insert Image</p>
          </TooltipContent>
        </Tooltip>

        {/* Horizontal Rule */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
              aria-label="Insert horizontal rule"
              className="h-8 w-8 p-0"
            >
              <MinusIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Horizontal Rule</p>
          </TooltipContent>
        </Tooltip>

        {/* Enhanced AI Tools Section */}
        {onTriggerAIPrompt && (
          <>
            <div className="mx-1.5 h-5 w-px bg-border"></div>

            {/* AI Dropdown Menu */}
            <DropdownMenu>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      disabled={aiDisabled}
                      className="ai-toolbar-button flex h-8 items-center gap-1 px-2.5"
                    >
                      <SparklesIcon className="h-4 w-4" />
                      <span>AI Tools</span>
                      <ChevronDownIcon className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <p>AI writing assistance tools</p>
                </TooltipContent>
              </Tooltip>
              <DropdownMenuContent align="start" className="w-56">
                <DropdownMenuItem
                  onClick={onTriggerAIPrompt}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4" />
                  <div className="flex flex-col">
                    <span>AI Assistant</span>
                    <span className="text-muted-foreground text-xs">
                      Generate, improve, or chat about your content
                    </span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => {
                    const selectedText = editor?.state.selection.empty
                      ? ''
                      : editor?.state.doc.textBetween(
                          editor.state.selection.from,
                          editor.state.selection.to
                        );

                    if (selectedText) {
                      // Trigger AI with improve mode
                      onTriggerAIPrompt();
                      // Note: We'll need to pass the mode to the AI dialog
                    } else {
                      import('sonner').then(({ toast }) => {
                        toast.info('Please select text to improve');
                      });
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4 text-blue-500" />
                  <div className="flex flex-col">
                    <span>Improve Writing</span>
                    <span className="text-muted-foreground text-xs">
                      Enhance selected text clarity and flow
                    </span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => {
                    const documentText = editor?.getText();
                    if (documentText && documentText.length > 50) {
                      onTriggerAIPrompt();
                      // Note: We'll need to pass the summarize mode
                    } else {
                      import('sonner').then(({ toast }) => {
                        toast.info('Document needs more content to summarize');
                      });
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4 text-green-500" />
                  <div className="flex flex-col">
                    <span>Summarize Document</span>
                    <span className="text-muted-foreground text-xs">
                      Create a concise summary of your document
                    </span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => {
                    const selectedText = editor?.state.selection.empty
                      ? ''
                      : editor?.state.doc.textBetween(
                          editor.state.selection.from,
                          editor.state.selection.to
                        );

                    if (selectedText) {
                      // Quick academic tone improvement
                      const prompt = `Please rewrite this text in an academic tone: "${selectedText}"`;
                      // We'll need to implement quick AI processing
                      import('sonner').then(({ toast }) => {
                        toast.info(
                          'Academic tone conversion - feature coming soon!'
                        );
                      });
                    } else {
                      import('sonner').then(({ toast }) => {
                        toast.info(
                          'Please select text to convert to academic tone'
                        );
                      });
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4 text-purple-500" />
                  <div className="flex flex-col">
                    <span>Academic Tone</span>
                    <span className="text-muted-foreground text-xs">
                      Convert selected text to academic style
                    </span>
                  </div>
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => {
                    const selectedText = editor?.state.selection.empty
                      ? ''
                      : editor?.state.doc.textBetween(
                          editor.state.selection.from,
                          editor.state.selection.to
                        );

                    if (selectedText) {
                      // Quick casual tone improvement
                      import('sonner').then(({ toast }) => {
                        toast.info(
                          'Casual tone conversion - feature coming soon!'
                        );
                      });
                    } else {
                      import('sonner').then(({ toast }) => {
                        toast.info(
                          'Please select text to convert to casual tone'
                        );
                      });
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-4 w-4 text-orange-500" />
                  <div className="flex flex-col">
                    <span>Casual Tone</span>
                    <span className="text-muted-foreground text-xs">
                      Convert selected text to casual style
                    </span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
    </TooltipProvider>
  );
};
