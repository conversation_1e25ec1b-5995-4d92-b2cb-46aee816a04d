// Content transfer utilities for sending content between flow nodes and word editor

export interface PendingContent {
  id: string;
  content: string;
  type: 'text' | 'html' | 'markdown';
  timestamp: number;
  source: string; // e.g., 'text-node', 'image-node', etc.
}

const PENDING_CONTENT_KEY = 'tersa_pending_word_editor_content';

/**
 * Store content to be transferred to the word editor
 */
export function storePendingContent(content: string, type: 'text' | 'html' | 'markdown' = 'html', source: string = 'flow-node'): string {
  try {
    const pendingContent: PendingContent = {
      id: Date.now().toString(36) + Math.random().toString(36).substring(2),
      content,
      type,
      timestamp: Date.now(),
      source,
    };

    localStorage.setItem(PENDING_CONTENT_KEY, JSON.stringify(pendingContent));
    return pendingContent.id;
  } catch (error) {
    console.error('Error storing pending content:', error);
    return '';
  }
}

/**
 * Get pending content for the word editor
 */
export function getPendingContent(): PendingContent | null {
  try {
    const stored = localStorage.getItem(PENDING_CONTENT_KEY);
    if (!stored) return null;
    
    const pendingContent = JSON.parse(stored) as PendingContent;
    
    // Check if content is not too old (24 hours)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (Date.now() - pendingContent.timestamp > maxAge) {
      clearPendingContent();
      return null;
    }
    
    return pendingContent;
  } catch (error) {
    console.error('Error getting pending content:', error);
    return null;
  }
}

/**
 * Clear pending content after it has been processed
 */
export function clearPendingContent(): void {
  try {
    localStorage.removeItem(PENDING_CONTENT_KEY);
  } catch (error) {
    console.error('Error clearing pending content:', error);
  }
}

/**
 * Send content to word editor (either directly if open, or store for later)
 */
export function sendContentToWordEditor(content: string, type: 'text' | 'html' | 'markdown' = 'html', source: string = 'flow-node'): boolean {
  // First try to add directly to the word editor if it's open
  if (typeof window !== 'undefined') {
    try {
      const { addContentToKiboEditor } = require('./kibo-editor-global');
      if (addContentToKiboEditor(content)) {
        return true; // Successfully added to open editor
      }
    } catch (error) {
      // Editor not available, continue to store for later
    }
  }
  
  // Store for later if editor is not open
  const id = storePendingContent(content, type, source);
  return !!id;
}

/**
 * Format content for the word editor based on type
 */
export function formatContentForEditor(content: string, type: 'text' | 'html' | 'markdown'): string {
  switch (type) {
    case 'text':
      // Convert plain text to HTML paragraphs
      return content
        .split('\n\n')
        .map(paragraph => paragraph.trim())
        .filter(paragraph => paragraph.length > 0)
        .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
        .join('');
    
    case 'markdown':
      // For now, do basic markdown to HTML conversion
      // In a real implementation, you might want to use a markdown parser
      return content
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^(.*)$/gim, '<p>$1</p>')
        .replace(/<p><\/p>/g, '');
    
    case 'html':
    default:
      return content;
  }
}
