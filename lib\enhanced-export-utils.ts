/**
 * Enhanced Export Utilities for Tersa Word Editor
 * Provides improved PDF and DOCX export functionality with better formatting
 */

import { Document, Paragraph, TextRun, HeadingLevel, Packer, AlignmentType } from 'docx';
import { saveAs } from 'file-saver';

export interface ExportDocument {
  title: string;
  content: string;
  author?: string;
  createdAt?: Date;
}

/**
 * Enhanced PDF Export using browser's print functionality
 * Provides better formatting and styling for PDF output
 */
export const exportToPdfEnhanced = async (document: ExportDocument): Promise<void> => {
  try {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('Unable to open print window. Please check popup blockers.');
    }

    // Clean and format the content
    const cleanContent = document.content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/class="[^"]*"/gi, '')
      .replace(/style="[^"]*"/gi, '');

    // Enhanced CSS for PDF export
    const pdfStyles = `
      <style>
        @page {
          margin: 1in;
          size: letter;
        }
        
        body {
          font-family: 'Times New Roman', serif;
          font-size: 12pt;
          line-height: 1.6;
          color: #000;
          background: white;
          margin: 0;
          padding: 0;
        }
        
        .document-header {
          text-align: center;
          margin-bottom: 2em;
          padding-bottom: 1em;
          border-bottom: 2px solid #333;
        }
        
        .document-title {
          font-size: 24pt;
          font-weight: bold;
          margin-bottom: 0.5em;
          color: #1a1a1a;
        }
        
        .document-meta {
          font-size: 10pt;
          color: #666;
          font-style: italic;
        }
        
        h1 {
          font-size: 20pt;
          font-weight: bold;
          margin: 2em 0 1em 0;
          page-break-after: avoid;
          color: #1a1a1a;
          border-bottom: 1px solid #ccc;
          padding-bottom: 0.5em;
        }
        
        h2 {
          font-size: 16pt;
          font-weight: bold;
          margin: 1.5em 0 0.75em 0;
          page-break-after: avoid;
          color: #2a2a2a;
        }
        
        h3 {
          font-size: 14pt;
          font-weight: bold;
          margin: 1.25em 0 0.5em 0;
          page-break-after: avoid;
          color: #3a3a3a;
        }
        
        p {
          margin-bottom: 1em;
          page-break-inside: avoid;
          text-align: justify;
          orphans: 2;
          widows: 2;
        }
        
        strong {
          font-weight: bold;
        }
        
        em {
          font-style: italic;
        }
        
        ul, ol {
          margin: 1em 0;
          padding-left: 2em;
          page-break-inside: avoid;
        }
        
        li {
          margin-bottom: 0.5em;
          page-break-inside: avoid;
        }
        
        blockquote {
          margin: 1.5em 2em;
          padding: 1em;
          border-left: 4px solid #666;
          background-color: #f9f9f9;
          font-style: italic;
          page-break-inside: avoid;
        }
        
        table {
          border-collapse: collapse;
          width: 100%;
          margin: 1em 0;
          page-break-inside: avoid;
        }
        
        th, td {
          border: 1px solid #333;
          padding: 8px 12px;
          text-align: left;
          vertical-align: top;
        }
        
        th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        
        code {
          font-family: 'Courier New', monospace;
          background-color: #f5f5f5;
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 11pt;
        }
        
        pre {
          font-family: 'Courier New', monospace;
          background-color: #f5f5f5;
          padding: 1em;
          border-radius: 5px;
          overflow-x: auto;
          page-break-inside: avoid;
          font-size: 10pt;
          line-height: 1.4;
        }
        
        .page-break {
          page-break-before: always;
        }
        
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
      </style>
    `;

    // Create the document meta information
    const documentMeta = `
      <div class="document-meta">
        ${document.author ? `Author: ${document.author} | ` : ''}
        Created: ${document.createdAt ? document.createdAt.toLocaleDateString() : new Date().toLocaleDateString()}
      </div>
    `;

    // Write the enhanced HTML content
    printWindow.document.write(`
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${document.title}</title>
          ${pdfStyles}
        </head>
        <body>
          <div class="document-header">
            <div class="document-title">${document.title}</div>
            ${documentMeta}
          </div>
          <div class="document-content">
            ${cleanContent}
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Wait for content to load, then print
    setTimeout(() => {
      printWindow.print();
      
      // Auto-close after a delay to improve UX
      setTimeout(() => {
        printWindow.close();
      }, 1500);
    }, 800);

    // Show success message
    if (typeof window !== 'undefined' && 'sonner' in window) {
      const { toast } = await import('sonner');
      toast.success('PDF export opened - you can save as PDF from the print dialog');
    }

  } catch (error) {
    console.error('PDF export error:', error);
    if (typeof window !== 'undefined' && 'sonner' in window) {
      const { toast } = await import('sonner');
      toast.error('Failed to export PDF. Please try again.');
    }
    throw error;
  }
};

/**
 * Enhanced DOCX Export with better formatting and structure
 */
export const exportToDocxEnhanced = async (document: ExportDocument): Promise<void> => {
  try {
    // Parse HTML content to extract structured data
    const parser = new DOMParser();
    const doc = parser.parseFromString(document.content, 'text/html');
    
    const children: any[] = [];
    
    // Add document title
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: document.title,
            bold: true,
            size: 32, // 16pt
          }),
        ],
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 },
      })
    );

    // Add document meta information
    if (document.author || document.createdAt) {
      const metaText = [
        document.author ? `Author: ${document.author}` : '',
        document.createdAt ? `Created: ${document.createdAt.toLocaleDateString()}` : `Created: ${new Date().toLocaleDateString()}`
      ].filter(Boolean).join(' | ');

      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: metaText,
              italics: true,
              size: 20, // 10pt
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 600 },
        })
      );
    }

    // Process content elements
    const processElement = (element: Element): any[] => {
      const results: any[] = [];
      
      for (const child of Array.from(element.children)) {
        const tagName = child.tagName.toLowerCase();
        const textContent = child.textContent || '';
        
        switch (tagName) {
          case 'h1':
            results.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: textContent,
                    bold: true,
                    size: 28, // 14pt
                  }),
                ],
                heading: HeadingLevel.HEADING_1,
                spacing: { before: 400, after: 200 },
              })
            );
            break;
            
          case 'h2':
            results.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: textContent,
                    bold: true,
                    size: 24, // 12pt
                  }),
                ],
                heading: HeadingLevel.HEADING_2,
                spacing: { before: 300, after: 150 },
              })
            );
            break;
            
          case 'h3':
            results.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: textContent,
                    bold: true,
                    size: 22, // 11pt
                  }),
                ],
                heading: HeadingLevel.HEADING_3,
                spacing: { before: 250, after: 100 },
              })
            );
            break;
            
          case 'p':
            if (textContent.trim()) {
              const textRuns: TextRun[] = [];
              
              // Simple text processing (could be enhanced for complex formatting)
              const strongElements = child.querySelectorAll('strong');
              const emElements = child.querySelectorAll('em');
              
              if (strongElements.length > 0 || emElements.length > 0) {
                // Complex formatting - simplified approach
                textRuns.push(
                  new TextRun({
                    text: textContent,
                    bold: strongElements.length > 0,
                    italics: emElements.length > 0,
                  })
                );
              } else {
                textRuns.push(new TextRun(textContent));
              }
              
              results.push(
                new Paragraph({
                  children: textRuns,
                  spacing: { after: 200 },
                })
              );
            }
            break;
            
          case 'blockquote':
            results.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: textContent,
                    italics: true,
                  }),
                ],
                spacing: { before: 200, after: 200 },
                indent: { left: 720 }, // 0.5 inch
              })
            );
            break;
            
          default:
            // Handle other elements or nested content
            if (textContent.trim()) {
              results.push(
                new Paragraph({
                  children: [new TextRun(textContent)],
                  spacing: { after: 200 },
                })
              );
            }
            break;
        }
      }
      
      return results;
    };

    // Process the document body
    const bodyElements = processElement(doc.body);
    children.push(...bodyElements);

    // Create the DOCX document
    const docxDocument = new Document({
      sections: [
        {
          properties: {
            page: {
              margin: {
                top: 1440,    // 1 inch
                right: 1440,  // 1 inch
                bottom: 1440, // 1 inch
                left: 1440,   // 1 inch
              },
            },
          },
          children,
        },
      ],
    });

    // Generate and download the file
    const blob = await Packer.toBlob(docxDocument);
    const fileName = `${document.title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.docx`;
    saveAs(blob, fileName);

    // Show success message
    if (typeof window !== 'undefined' && 'sonner' in window) {
      const { toast } = await import('sonner');
      toast.success(`Document exported as ${fileName}`);
    }

  } catch (error) {
    console.error('DOCX export error:', error);
    if (typeof window !== 'undefined' && 'sonner' in window) {
      const { toast } = await import('sonner');
      toast.error('Failed to export DOCX. Please try again.');
    }
    throw error;
  }
};
