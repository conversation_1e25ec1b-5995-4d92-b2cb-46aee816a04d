import { createBrowserClient } from '@supabase/ssr';
import { env } from '../env';

// Mock Supabase client for browser (development)
const mockSupabaseClient = {
  auth: {
    getUser: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
          app_metadata: {},
          user_metadata: {},
          created_at: new Date().toISOString(),
        },
      },
      error: null,
    }),
    signInWithPassword: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: {
          access_token: 'mock-access-token',
        },
      },
      error: null,
    }),
    signUp: async () => ({
      data: {
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
        },
        session: null,
      },
      error: null,
    }),
    resetPasswordForEmail: async () => ({ error: null }),
    updateUser: async () => ({ error: null }),
    signInWithOAuth: async () => ({ error: null }),
  },
  storage: {
    from: (bucket: string) => ({
      upload: async (
        path: string,
        file: File,
        _options?: { contentType?: string; upsert?: boolean }
      ) => {
        // In development, convert file to base64 data URL for server compatibility
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => {
            const dataUrl = reader.result as string;
            const fileInfo = {
              bucket,
              path,
              dataUrl,
              contentType: file.type,
              name: file.name,
              size: file.size,
              uploadedAt: new Date().toISOString(),
            };

            // Store file info in localStorage for persistence during development
            try {
              const storageKey = `mock-file-${bucket}-${path}`;
              localStorage.setItem(storageKey, JSON.stringify(fileInfo));
            } catch (_error) {
              // Silent fail in development
            }

            resolve({
              data: {
                path: `${bucket}/${path}`,
                id: `mock-file-id-${Date.now()}`,
                fullPath: `${bucket}/${path}`,
              },
              error: null,
            });
          };

          // Convert file to data URL (base64)
          reader.readAsDataURL(file);
        });
      },
      getPublicUrl: (path: string) => {
        // Try to get the data URL from localStorage
        try {
          const storageKey = `mock-file-${path.replace('/', '-')}`;
          const fileInfo = localStorage.getItem(storageKey);
          if (fileInfo) {
            const { dataUrl } = JSON.parse(fileInfo);
            return {
              data: {
                publicUrl: dataUrl,
              },
            };
          }
        } catch (_error) {
          // Silent fail in development
        }

        // Fallback to a placeholder data URL
        const fileName = path.split('/').pop() || 'unknown';
        const placeholderText = `Mock file: ${fileName}`;
        return {
          data: {
            publicUrl: `data:text/plain;base64,${btoa(placeholderText)}`,
          },
        };
      },
    }),
  },
};

export function createClient() {
  // Use mock client in development
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return mockSupabaseClient as unknown as ReturnType<
      typeof createBrowserClient
    >;
  }
  // Use correct arguments for createBrowserClient
  return createBrowserClient(
    env.NEXT_PUBLIC_SUPABASE_URL || 'https://example.supabase.co',
    env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy-key',
    {
      // Optionally, add cookie options or other config if needed
    }
  );
}
