// Test script to verify file upload functionality
// This can be run from the browser console to test uploads

const testFileUpload = () => {
  // Create a test file
  const testContent = 'This is a test PDF file content';
  const testFile = new File([testContent], 'test-document.pdf', {
    type: 'application/pdf',
    lastModified: Date.now(),
  });

  console.log('Test file created:', {
    name: testFile.name,
    size: testFile.size,
    type: testFile.type,
  });

  // Test the upload function
  if (typeof uploadFile !== 'undefined') {
    uploadFile(testFile, 'files')
      .then((result) => {
        console.log('Upload successful:', result);
      })
      .catch((error) => {
        console.error('Upload failed:', error);
      });
  } else {
    console.error('uploadFile function not available');
  }
};

// Instructions for running the test
console.log('To test file upload, run: testFileUpload()');
